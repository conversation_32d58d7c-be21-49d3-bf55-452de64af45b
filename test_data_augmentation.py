#!/usr/bin/env python3
"""
数据增强功能测试脚本

测试新添加的数据增强功能是否正常工作
"""

import os
import sys
import torch
import numpy as np
import argparse

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.datasets.mvtec import MVTecDataset, DatasetSplit
from src.datasets.visa import VISADataset


def test_augmentation_basic():
    """测试基本数据增强功能"""
    print("=" * 50)
    print("测试基本数据增强功能")
    print("=" * 50)
    
    try:
        # 测试MVTec数据集
        print("1. 测试MVTec数据集...")
        
        # 无增强
        dataset_no_aug = MVTecDataset(
            source="./mvtec_anomaly_detection",
            classname="bottle",
            resize=256,
            imagesize=224,
            split=DatasetSplit.TRAIN,
            enable_augmentation=False
        )
        
        # 有增强
        dataset_with_aug = MVTecDataset(
            source="./mvtec_anomaly_detection",
            classname="bottle",
            resize=256,
            imagesize=224,
            split=DatasetSplit.TRAIN,
            enable_augmentation=True,
            augmentation_strength=0.5
        )
        
        print(f"   ✓ 无增强数据集创建成功，样本数: {len(dataset_no_aug)}")
        print(f"   ✓ 有增强数据集创建成功，样本数: {len(dataset_with_aug)}")
        
        # 测试样本加载
        sample_no_aug = dataset_no_aug[0]
        sample_with_aug = dataset_with_aug[0]
        
        print(f"   ✓ 无增强样本形状: {sample_no_aug['image'].shape}")
        print(f"   ✓ 有增强样本形状: {sample_with_aug['image'].shape}")
        
        # 测试VISA数据集
        print("\n2. 测试VISA数据集...")
        try:
            dataset_visa = VISADataset(
                source="./visa",
                classname="capsules",
                resize=256,
                imagesize=224,
                split=DatasetSplit.TRAIN,
                enable_augmentation=True,
                augmentation_strength=0.3
            )
            print(f"   ✓ VISA数据集创建成功，样本数: {len(dataset_visa)}")
            
            sample_visa = dataset_visa[0]
            print(f"   ✓ VISA样本形状: {sample_visa['image'].shape}")
            
        except Exception as e:
            print(f"   ⚠ VISA数据集测试跳过: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False


def test_augmentation_strengths():
    """测试不同增强强度"""
    print("\n" + "=" * 50)
    print("测试不同增强强度")
    print("=" * 50)
    
    strengths = [0.0, 0.3, 0.5, 0.8, 1.0]
    
    for strength in strengths:
        try:
            dataset = MVTecDataset(
                source="./mvtec_anomaly_detection",
                classname="bottle",
                resize=256,
                imagesize=224,
                split=DatasetSplit.TRAIN,
                enable_augmentation=True,
                augmentation_strength=strength
            )
            
            # 加载几个样本测试
            for i in range(min(3, len(dataset))):
                sample = dataset[i]
                assert sample['image'].shape == (3, 224, 224), f"样本形状错误: {sample['image'].shape}"
            
            print(f"✓ 强度 {strength}: 测试通过")
            
        except Exception as e:
            print(f"✗ 强度 {strength}: 测试失败 - {e}")
            return False
    
    return True


def test_augmentation_consistency():
    """测试增强的一致性"""
    print("\n" + "=" * 50)
    print("测试增强一致性")
    print("=" * 50)
    
    try:
        dataset = MVTecDataset(
            source="./mvtec_anomaly_detection",
            classname="bottle",
            resize=256,
            imagesize=224,
            split=DatasetSplit.TRAIN,
            enable_augmentation=True,
            augmentation_strength=0.5
        )
        
        # 测试同一个样本的多次加载（应该产生不同的增强结果）
        sample_idx = 0
        samples = []
        
        for i in range(5):
            sample = dataset[sample_idx]
            samples.append(sample['image'])
        
        # 检查是否产生了不同的结果（由于随机性）
        all_same = True
        for i in range(1, len(samples)):
            if not torch.equal(samples[0], samples[i]):
                all_same = False
                break
        
        if all_same:
            print("⚠ 警告: 多次加载产生相同结果，可能随机性不足")
        else:
            print("✓ 随机增强正常工作")
        
        # 测试数据类型和范围
        sample = dataset[0]
        image = sample['image']
        
        assert image.dtype == torch.float32, f"数据类型错误: {image.dtype}"
        print(f"✓ 数据类型正确: {image.dtype}")
        
        # 检查归一化范围（大致在[-2, 2]之间，因为ImageNet归一化）
        min_val, max_val = image.min().item(), image.max().item()
        if -3 < min_val < 3 and -3 < max_val < 3:
            print(f"✓ 数值范围正常: [{min_val:.3f}, {max_val:.3f}]")
        else:
            print(f"⚠ 数值范围异常: [{min_val:.3f}, {max_val:.3f}]")
        
        return True
        
    except Exception as e:
        print(f"✗ 一致性测试失败: {e}")
        return False


def test_training_vs_testing():
    """测试训练和测试模式的区别"""
    print("\n" + "=" * 50)
    print("测试训练/测试模式区别")
    print("=" * 50)
    
    try:
        # 训练模式（应该有增强）
        train_dataset = MVTecDataset(
            source="./mvtec_anomaly_detection",
            classname="bottle",
            resize=256,
            imagesize=224,
            split=DatasetSplit.TRAIN,
            enable_augmentation=True,
            augmentation_strength=0.5
        )
        
        # 测试模式（不应该有增强）
        test_dataset = MVTecDataset(
            source="./mvtec_anomaly_detection",
            classname="bottle",
            resize=256,
            imagesize=224,
            split=DatasetSplit.TEST,
            enable_augmentation=True,  # 即使设置为True
            augmentation_strength=0.5
        )
        
        print(f"✓ 训练数据集创建成功，样本数: {len(train_dataset)}")
        print(f"✓ 测试数据集创建成功，样本数: {len(test_dataset)}")
        
        # 检查变换管道
        train_transforms = str(train_dataset.transform_img)
        test_transforms = str(test_dataset.transform_img)
        
        # 训练模式应该包含更多变换
        if "RandomRotation" in train_transforms or "RandomAffine" in train_transforms:
            print("✓ 训练模式包含数据增强")
        else:
            print("⚠ 训练模式可能缺少数据增强")
        
        # 测试模式应该只有基本变换
        if "RandomRotation" not in test_transforms and "RandomAffine" not in test_transforms:
            print("✓ 测试模式不包含数据增强")
        else:
            print("⚠ 测试模式意外包含数据增强")
        
        return True
        
    except Exception as e:
        print(f"✗ 训练/测试模式测试失败: {e}")
        return False


def test_integration_with_main():
    """测试与main.py的集成"""
    print("\n" + "=" * 50)
    print("测试与main.py的集成")
    print("=" * 50)
    
    try:
        # 模拟main.py的参数
        class Args:
            def __init__(self):
                self.enable_augmentation = True
                self.augmentation_strength = 0.5
        
        args = Args()
        
        # 测试参数传递
        from src.datasets.mvtec import MVTecDataset, DatasetSplit
        
        dataset = MVTecDataset(
            source="./mvtec_anomaly_detection",
            classname="bottle",
            resize=256,
            imagesize=224,
            split=DatasetSplit.TRAIN,
            enable_augmentation=args.enable_augmentation,
            augmentation_strength=args.augmentation_strength
        )
        
        print("✓ 参数传递正常")
        print(f"✓ 数据集创建成功，样本数: {len(dataset)}")
        
        # 测试样本加载
        sample = dataset[0]
        print(f"✓ 样本加载成功，形状: {sample['image'].shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ 集成测试失败: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='测试数据增强功能')
    parser.add_argument('--test', choices=['basic', 'strengths', 'consistency', 'modes', 'integration', 'all'], 
                       default='all', help='选择测试类型')
    
    args = parser.parse_args()
    
    print("数据增强功能测试")
    print("=" * 60)
    
    success_count = 0
    total_tests = 0
    
    tests = []
    if args.test in ['basic', 'all']:
        tests.append(('基本功能', test_augmentation_basic))
    if args.test in ['strengths', 'all']:
        tests.append(('增强强度', test_augmentation_strengths))
    if args.test in ['consistency', 'all']:
        tests.append(('一致性', test_augmentation_consistency))
    if args.test in ['modes', 'all']:
        tests.append(('训练/测试模式', test_training_vs_testing))
    if args.test in ['integration', 'all']:
        tests.append(('集成', test_integration_with_main))
    
    for test_name, test_func in tests:
        total_tests += 1
        print(f"\n运行测试: {test_name}")
        if test_func():
            success_count += 1
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {success_count}")
    print(f"失败测试: {total_tests - success_count}")
    
    if success_count == total_tests:
        print("✓ 所有测试通过！数据增强功能正常工作")
        print("\n现在可以使用以下命令启用数据增强:")
        print("python main.py --dataset visa --data_path ./visa --subdatasets capsules \\")
        print("    --enable_augmentation --augmentation_strength 0.5 \\")
        print("    --enable_improved_alignment --alignment_method spatial")
    else:
        print("✗ 部分测试失败，请检查错误信息")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
