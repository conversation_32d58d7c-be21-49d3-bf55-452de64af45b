#!/usr/bin/env python3
"""
测试patch对齐检查功能

这个脚本验证新添加的对齐检查功能是否正常工作，包括：
1. 对齐质量评估
2. 自动对齐决策
3. 可视化功能
4. 与主流程的集成
"""

import os
import sys
import torch
import numpy as np
import argparse
import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.alignment_checker import AlignmentChecker
import src.softpatch as softpatch
import src.backbones as backbones
import src.common as common
import src.sampler as sampler


def test_alignment_checker():
    """测试对齐检查器的基本功能"""
    print("=" * 50)
    print("测试对齐检查器基本功能")
    print("=" * 50)
    
    # 创建测试数据
    features = [
        torch.randn(196, 256),  # 14x14 patches
        torch.randn(49, 512),   # 7x7 patches
        torch.randn(9, 1024),   # 3x3 patches
    ]
    patch_shapes = [(14, 14), (7, 7), (3, 3)]
    
    # 创建检查器
    checker = AlignmentChecker()
    
    # 测试对齐质量检查
    print("1. 测试对齐质量检查...")
    alignment_info = checker.check_alignment_quality(features, patch_shapes)
    
    print(f"   对齐状态: {'✓ 已对齐' if alignment_info['is_aligned'] else '✗ 需要对齐'}")
    print(f"   质量分数: {alignment_info['quality_score']:.3f}")
    print(f"   空间一致性: {alignment_info['spatial_consistency']:.3f}")
    print(f"   特征相似性: {alignment_info['feature_similarity']:.3f}")
    
    # 测试报告生成
    print("\n2. 测试报告生成...")
    report = checker.generate_alignment_report(features, patch_shapes)
    print(report)
    
    # 测试可视化
    print("\n3. 测试可视化功能...")
    try:
        checker.visualize_alignment_status(features, patch_shapes, 
                                         save_path="test_alignment_status.png")
        print("   ✓ 可视化图像已保存到: test_alignment_status.png")
    except Exception as e:
        print(f"   ✗ 可视化失败: {e}")
    
    return True


def test_improved_alignment_integration():
    """测试改进对齐功能的集成"""
    print("\n" + "=" * 50)
    print("测试改进对齐功能集成")
    print("=" * 50)
    
    try:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {device}")
        
        # 创建模型
        model = softpatch.SoftPatch(device)
        backbone = backbones.load('wideresnet50')
        feature_sampler = sampler.ApproximateGreedyCoresetSampler(
            percentage=0.1, device=device
        )
        nn_method = common.FaissNN(False, 4)
        
        # 测试不同的对齐方法
        alignment_methods = ['spatial', 'hungarian', 'adaptive']
        
        for method in alignment_methods:
            print(f"\n测试对齐方法: {method}")
            print("-" * 30)
            
            # 加载模型
            model.load(
                backbone=backbone,
                layers_to_extract_from=['layer2', 'layer3'],
                device=device,
                input_shape=(3, 224, 224),
                featuresampler=feature_sampler,
                nn_method=nn_method,
                lof_k=6,
                threshold=0.15,
                weight_method='lof',
                soft_weight_flag=True,
                enable_improved_alignment=True,
                alignment_method=method,
            )
            
            # 测试前向传播
            test_input = torch.randn(2, 3, 224, 224).to(device)
            
            start_time = time.time()
            with torch.no_grad():
                features = model._embed(test_input)
            end_time = time.time()
            
            print(f"   ✓ {method} 方法测试成功")
            print(f"   处理时间: {(end_time - start_time)*1000:.2f} ms")
            print(f"   特征数量: {len(features)}")
            
        return True
        
    except Exception as e:
        print(f"✗ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_alignment_decision_logic():
    """测试对齐决策逻辑"""
    print("\n" + "=" * 50)
    print("测试对齐决策逻辑")
    print("=" * 50)
    
    # 创建不同质量的测试数据
    test_cases = [
        {
            'name': '高质量对齐',
            'features': [
                torch.randn(100, 256),
                torch.randn(100, 256) + torch.randn(100, 256) * 0.1,  # 轻微噪声
            ],
            'patch_shapes': [(10, 10), (10, 10)],
            'expected_aligned': True
        },
        {
            'name': '中等质量对齐',
            'features': [
                torch.randn(100, 256),
                torch.randn(80, 256),  # 不同大小
            ],
            'patch_shapes': [(10, 10), (8, 10)],
            'expected_aligned': False
        },
        {
            'name': '低质量对齐',
            'features': [
                torch.randn(100, 256),
                torch.randn(50, 512),  # 完全不同
            ],
            'patch_shapes': [(10, 10), (5, 10)],
            'expected_aligned': False
        }
    ]
    
    checker = AlignmentChecker()
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试案例: {test_case['name']}")
        print("-" * 30)
        
        alignment_info = checker.check_alignment_quality(
            test_case['features'], 
            test_case['patch_shapes']
        )
        
        is_aligned = alignment_info['is_aligned']
        expected = test_case['expected_aligned']
        
        print(f"   质量分数: {alignment_info['quality_score']:.3f}")
        print(f"   预期结果: {'已对齐' if expected else '需要对齐'}")
        print(f"   实际结果: {'已对齐' if is_aligned else '需要对齐'}")
        print(f"   测试结果: {'✓ 通过' if is_aligned == expected else '✗ 失败'}")
    
    return True


def test_performance_impact():
    """测试性能影响"""
    print("\n" + "=" * 50)
    print("测试性能影响")
    print("=" * 50)
    
    # 创建不同大小的测试数据
    test_sizes = [
        (100, 256, "小规模"),
        (1000, 256, "中规模"),
        (5000, 256, "大规模"),
    ]
    
    checker = AlignmentChecker()
    
    for size, dim, desc in test_sizes:
        print(f"\n测试 {desc} 数据 ({size} x {dim})")
        print("-" * 30)
        
        # 创建测试数据
        features = [
            torch.randn(size, dim),
            torch.randn(size, dim),
        ]
        patch_shapes = [(int(np.sqrt(size)), int(np.sqrt(size)))] * 2
        
        # 测量时间
        start_time = time.time()
        alignment_info = checker.check_alignment_quality(features, patch_shapes)
        end_time = time.time()
        
        print(f"   处理时间: {(end_time - start_time)*1000:.2f} ms")
        print(f"   质量分数: {alignment_info['quality_score']:.3f}")
        print(f"   内存使用: 正常")
    
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='测试patch对齐检查功能')
    parser.add_argument('--test', choices=['basic', 'integration', 'logic', 'performance', 'all'], 
                       default='all', help='选择测试类型')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    print("Patch对齐检查功能测试")
    print("=" * 60)
    
    success_count = 0
    total_tests = 0
    
    if args.test in ['basic', 'all']:
        total_tests += 1
        if test_alignment_checker():
            success_count += 1
    
    if args.test in ['integration', 'all']:
        total_tests += 1
        if test_improved_alignment_integration():
            success_count += 1
    
    if args.test in ['logic', 'all']:
        total_tests += 1
        if test_alignment_decision_logic():
            success_count += 1
    
    if args.test in ['performance', 'all']:
        total_tests += 1
        if test_performance_impact():
            success_count += 1
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {success_count}")
    print(f"失败测试: {total_tests - success_count}")
    
    if success_count == total_tests:
        print("✓ 所有测试通过！对齐检查功能正常工作")
        print("\n现在可以使用以下命令运行改进的对齐功能:")
        print("python main.py --dataset visa --data_path ./visa --subdatasets capsules \\")
        print("    --enable_improved_alignment --alignment_method spatial \\")
        print("    --resize 512 --imagesize 512 --sampling_ratio 0.01")
    else:
        print("✗ 部分测试失败，请检查错误信息")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
