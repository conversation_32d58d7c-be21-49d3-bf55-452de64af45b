#!/usr/bin/env python3
"""
修复stack错误的测试脚本
"""

import os
import sys
import torch
import numpy as np
import traceback

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_feature_consistency():
    """测试特征一致性"""
    print("测试特征一致性...")
    
    try:
        import src.softpatch as softpatch
        import src.backbones as backbones
        import src.common as common
        import src.sampler as sampler
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {device}")
        
        # 测试不同的配置
        test_configs = [
            {
                'batch_size': 1,
                'layers': ['layer2', 'layer3'],
                'alignment': (False, 'spatial'),
                'name': '原始方法_小批次'
            },
            {
                'batch_size': 2,
                'layers': ['layer2', 'layer3'],
                'alignment': (False, 'spatial'),
                'name': '原始方法_中批次'
            },
            {
                'batch_size': 1,
                'layers': ['layer2', 'layer3'],
                'alignment': (True, 'spatial'),
                'name': '空间对齐_小批次'
            },
            {
                'batch_size': 2,
                'layers': ['layer2', 'layer3'],
                'alignment': (True, 'spatial'),
                'name': '空间对齐_中批次'
            },
            {
                'batch_size': 1,
                'layers': ['layer2'],  # 只使用一层
                'alignment': (True, 'hungarian'),
                'name': '匈牙利对齐_单层'
            },
        ]
        
        backbone = backbones.load('wideresnet50')
        
        for config in test_configs:
            print(f"\n{'='*50}")
            print(f"测试配置: {config['name']}")
            print(f"{'='*50}")
            
            try:
                # 创建模型
                model = softpatch.SoftPatch(device)
                feature_sampler = sampler.ApproximateGreedyCoresetSampler(
                    percentage=0.1, device=device
                )
                nn_method = common.FaissNN(False, 4)
                
                enable_improved, alignment_method = config['alignment']
                
                model.load(
                    backbone=backbone,
                    layers_to_extract_from=config['layers'],
                    device=device,
                    input_shape=(3, 224, 224),
                    featuresampler=feature_sampler,
                    nn_method=nn_method,
                    lof_k=6,
                    threshold=0.15,
                    weight_method='lof',
                    soft_weight_flag=True,
                    enable_improved_alignment=enable_improved,
                    alignment_method=alignment_method,
                )
                
                # 测试前向传播
                test_input = torch.randn(config['batch_size'], 3, 224, 224).to(device)
                
                print(f"输入形状: {test_input.shape}")
                
                with torch.no_grad():
                    # 测试特征提取
                    features, patch_shapes = model._embed(test_input, provide_patch_shapes=True)
                
                print(f"✓ 特征提取成功")
                print(f"  特征形状: {[f.shape if isinstance(f, torch.Tensor) else np.array(f).shape for f in features]}")
                print(f"  Patch形状: {patch_shapes}")
                
                # 检查特征一致性
                if isinstance(features, list) and len(features) > 1:
                    feature_sizes = [f.shape[0] if isinstance(f, torch.Tensor) else f.shape[0] for f in features]
                    if len(set(feature_sizes)) == 1:
                        print(f"✓ 特征大小一致: {feature_sizes[0]}")
                    else:
                        print(f"⚠ 特征大小不一致: {feature_sizes}")
                
                # 测试训练过程（简化版）
                try:
                    # 创建简单的训练数据
                    train_data = [test_input]
                    
                    # 模拟训练过程的关键步骤
                    all_features = []
                    for batch in train_data:
                        batch_features = model._embed(batch.to(torch.float).to(device))
                        all_features.extend(batch_features)
                    
                    print(f"✓ 训练数据处理成功")
                    print(f"  总特征数: {len(all_features)}")
                    
                except Exception as e:
                    print(f"⚠ 训练过程测试失败: {e}")
                
            except Exception as e:
                print(f"✗ 配置测试失败: {e}")
                print("错误详情:")
                traceback.print_exc()
                continue
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        traceback.print_exc()
        return False

def test_stack_operations():
    """测试可能导致stack错误的操作"""
    print(f"\n{'='*60}")
    print("测试stack操作...")
    print(f"{'='*60}")
    
    # 模拟不同大小的张量
    tensors = [
        torch.randn(6272, 1024),
        torch.randn(1568, 1024),
        torch.randn(3136, 1024),
    ]
    
    print("原始张量大小:")
    for i, t in enumerate(tensors):
        print(f"  张量{i}: {t.shape}")
    
    # 测试调整大小的方法
    print("\n测试大小调整方法:")
    
    # 方法1: 截断到最小大小
    min_size = min(t.shape[0] for t in tensors)
    truncated = [t[:min_size] for t in tensors]
    print(f"方法1 - 截断到最小大小 {min_size}:")
    for i, t in enumerate(truncated):
        print(f"  张量{i}: {t.shape}")
    
    try:
        stacked = torch.stack(truncated, dim=0)
        print(f"✓ Stack成功: {stacked.shape}")
    except Exception as e:
        print(f"✗ Stack失败: {e}")
    
    # 方法2: 填充到最大大小
    max_size = max(t.shape[0] for t in tensors)
    padded = []
    for t in tensors:
        if t.shape[0] < max_size:
            padding_size = max_size - t.shape[0]
            # 创建正确维度的repeat参数
            repeat_dims = [padding_size] + [1] * (len(t.shape) - 1)
            padding = t[-1:].repeat(*repeat_dims)
            padded_tensor = torch.cat([t, padding], dim=0)
        else:
            padded_tensor = t
        padded.append(padded_tensor)
    
    print(f"\n方法2 - 填充到最大大小 {max_size}:")
    for i, t in enumerate(padded):
        print(f"  张量{i}: {t.shape}")
    
    try:
        stacked = torch.stack(padded, dim=0)
        print(f"✓ Stack成功: {stacked.shape}")
    except Exception as e:
        print(f"✗ Stack失败: {e}")

def provide_solutions():
    """提供解决方案"""
    print(f"\n{'='*60}")
    print("Stack错误解决方案:")
    print(f"{'='*60}")
    
    print("1. 问题原因:")
    print("   - 不同层的特征经过patch化后大小不一致")
    print("   - torch.stack要求所有张量大小完全相同")
    print("   - 批次大小或图像尺寸变化导致特征大小变化")
    
    print("\n2. 已实施的修复:")
    print("   ✓ 在_spatial_alignment中添加大小一致性检查")
    print("   ✓ 在_adaptive_alignment中添加大小调整逻辑")
    print("   ✓ 在_compute_patch_weight中添加reshape错误处理")
    print("   ✓ 添加特征填充和截断机制")
    
    print("\n3. 使用建议:")
    print("   - 使用固定的batch_size进行训练")
    print("   - 确保输入图像尺寸一致")
    print("   - 优先使用'spatial'对齐方法（最稳定）")
    print("   - 如果问题持续，尝试只使用单层特征")
    
    print("\n4. 测试命令:")
    print("   python fix_stack_error.py  # 运行此脚本")
    print("   python quick_test_alignment.py --skip-performance")
    print("   python main.py --batch_size 1 --enable_improved_alignment --alignment_method spatial")

def main():
    """主函数"""
    print("Stack错误修复测试")
    print("=" * 60)
    
    # 测试特征一致性
    success = test_feature_consistency()
    
    # 测试stack操作
    test_stack_operations()
    
    # 提供解决方案
    provide_solutions()
    
    if success:
        print(f"\n{'='*60}")
        print("✓ 测试完成！大部分配置应该可以正常工作。")
        print("如果仍有问题，请尝试使用更保守的配置。")
        print("=" * 60)
    else:
        print(f"\n{'='*60}")
        print("⚠ 测试中发现问题，请查看上面的错误信息。")
        print("建议先使用原始方法测试基本功能。")
        print("=" * 60)

if __name__ == "__main__":
    main()
