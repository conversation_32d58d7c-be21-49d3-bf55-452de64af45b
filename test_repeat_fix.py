#!/usr/bin/env python3
"""
测试repeat操作修复的脚本
"""

import torch
import numpy as np

def test_repeat_operations():
    """测试不同维度的repeat操作"""
    print("测试repeat操作修复...")
    
    # 测试不同维度的张量
    test_cases = [
        ("2D张量", torch.randn(10, 256)),
        ("3D张量", torch.randn(10, 14, 256)),
        ("4D张量", torch.randn(10, 14, 14, 256)),
    ]
    
    for name, tensor in test_cases:
        print(f"\n测试 {name}: {tensor.shape}")
        
        try:
            # 原始的错误方法（会失败）
            try:
                padding_size = 5
                wrong_padding = tensor[-1:].repeat(padding_size, 1)
                print(f"  ✗ 错误方法成功（不应该发生）: {wrong_padding.shape}")
            except Exception as e:
                print(f"  ✓ 错误方法正确失败: {e}")
            
            # 修复后的正确方法
            padding_size = 5
            repeat_dims = [padding_size] + [1] * (len(tensor.shape) - 1)
            correct_padding = tensor[-1:].repeat(*repeat_dims)
            result = torch.cat([tensor, correct_padding], dim=0)
            
            print(f"  ✓ 正确方法成功: {tensor.shape} -> {result.shape}")
            print(f"    repeat_dims: {repeat_dims}")
            print(f"    padding形状: {correct_padding.shape}")
            
        except Exception as e:
            print(f"  ✗ 正确方法失败: {e}")

def test_alignment_simulation():
    """模拟对齐过程中的repeat操作"""
    print(f"\n{'='*50}")
    print("模拟特征对齐过程...")
    print(f"{'='*50}")
    
    # 模拟不同层的特征
    features = [
        torch.randn(6272, 1024),  # 第一层特征
        torch.randn(1568, 1024),  # 第二层特征（较小）
        torch.randn(3136, 1024),  # 第三层特征（中等）
    ]
    
    print("原始特征大小:")
    for i, feat in enumerate(features):
        print(f"  特征{i}: {feat.shape}")
    
    # 使用修复后的方法进行大小对齐
    target_size = features[0].shape[0]  # 使用第一个特征的大小作为目标
    aligned_features = []
    
    for i, feature in enumerate(features):
        print(f"\n处理特征{i}: {feature.shape}")
        
        if i == 0:
            aligned_features.append(feature)
            print(f"  参考特征，保持不变")
        else:
            if feature.shape[0] == target_size:
                aligned_features.append(feature)
                print(f"  大小匹配，保持不变")
            elif feature.shape[0] > target_size:
                # 截断
                adjusted_feature = feature[:target_size]
                aligned_features.append(adjusted_feature)
                print(f"  截断: {feature.shape} -> {adjusted_feature.shape}")
            else:
                # 填充
                padding_size = target_size - feature.shape[0]
                repeat_dims = [padding_size] + [1] * (len(feature.shape) - 1)
                padding = feature[-1:].repeat(*repeat_dims)
                adjusted_feature = torch.cat([feature, padding], dim=0)
                aligned_features.append(adjusted_feature)
                print(f"  填充: {feature.shape} -> {adjusted_feature.shape}")
                print(f"    填充大小: {padding_size}, repeat_dims: {repeat_dims}")
    
    print(f"\n对齐后的特征大小:")
    for i, feat in enumerate(aligned_features):
        print(f"  特征{i}: {feat.shape}")
    
    # 验证是否可以stack
    try:
        stacked = torch.stack(aligned_features, dim=0)
        print(f"\n✓ Stack成功: {stacked.shape}")
        return True
    except Exception as e:
        print(f"\n✗ Stack失败: {e}")
        return False

def test_edge_cases():
    """测试边界情况"""
    print(f"\n{'='*50}")
    print("测试边界情况...")
    print(f"{'='*50}")
    
    edge_cases = [
        ("空张量", torch.empty(0, 256)),
        ("单行张量", torch.randn(1, 256)),
        ("大张量", torch.randn(10000, 256)),
        ("高维张量", torch.randn(10, 5, 5, 256)),
    ]
    
    for name, tensor in edge_cases:
        print(f"\n测试 {name}: {tensor.shape}")
        
        try:
            if tensor.shape[0] == 0:
                print(f"  跳过空张量")
                continue
                
            padding_size = 3
            repeat_dims = [padding_size] + [1] * (len(tensor.shape) - 1)
            padding = tensor[-1:].repeat(*repeat_dims)
            result = torch.cat([tensor, padding], dim=0)
            
            print(f"  ✓ 成功: {tensor.shape} -> {result.shape}")
            
        except Exception as e:
            print(f"  ✗ 失败: {e}")

def main():
    """主函数"""
    print("Repeat操作修复测试")
    print("=" * 60)
    
    # 测试基本repeat操作
    test_repeat_operations()
    
    # 测试对齐模拟
    success = test_alignment_simulation()
    
    # 测试边界情况
    test_edge_cases()
    
    print(f"\n{'='*60}")
    if success:
        print("✓ Repeat操作修复测试通过！")
        print("现在可以安全使用改进的对齐方法。")
    else:
        print("⚠ 部分测试失败，请检查实现。")
    
    print("\n使用建议:")
    print("1. 现在可以运行: python quick_test_alignment.py")
    print("2. 或者运行: python fix_stack_error.py")
    print("3. 使用改进的对齐: python main.py --enable_improved_alignment")
    print("=" * 60)

if __name__ == "__main__":
    main()
