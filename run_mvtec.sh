datapath=.\mvtec_anomaly_detection
datasets=('bottle' 'cable' 'capsule' 'carpet' 'grid' 'hazelnut'
'leather' 'metal_nut' 'pill' 'screw' 'tile' 'toothbrush' 'transistor' 'wood' 'zipper')
dataset_flags=($(for dataset in "${datasets[@]}"; do echo '--subdatasets '$dataset; done))

python main.py --dataset mvtec --data_path ./mvtec_anomaly_detection --noise 0.1  "${dataset_flags[@]}" --gpu 0

python main.py --dataset mvtec --data_path ./mvtec_anomaly_detection --subdatasets bottle --subdatasets cable --subdatasets capsule --subdatasets carpet --subdatasets grid --subdatasets hazelnut --subdatasets leather --subdatasets metal_nut --subdatasets pill --subdatasets screw --subdatasets tile --subdatasets toothbrush --subdatasets transistor --subdatasets wood --subdatasets zipper   --noise 0.08 --gpu 0  "${dataset_flags[@]}" --seed 0 --resize 512 --imagesize 512 --sampling_ratio 0.01