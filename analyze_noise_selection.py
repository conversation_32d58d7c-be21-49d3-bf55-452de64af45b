#!/usr/bin/env python3
"""
分析噪声选取机制

详细分析SoftPatch如何从测试集选取噪声样本注入训练集
"""

import os
import sys
import torch
import numpy as np
import random
from collections import defaultdict, Counter

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.datasets.mvtec import MVTecDataset, DatasetSplit
from src.datasets.visa import VISADataset


def analyze_noise_selection_mechanism():
    """分析噪声选取机制"""
    print("=" * 60)
    print("SoftPatch噪声选取机制分析")
    print("=" * 60)
    
    print("📋 噪声选取流程:")
    print("1. 从测试集中找出所有异常样本 (is_anomaly=1)")
    print("2. 计算需要的噪声样本数量 = noise_ratio × 训练集大小")
    print("3. 从异常样本中随机选取指定数量的样本")
    print("4. 将选取的样本添加到训练集中")
    print("5. 可选择是否从测试集中移除这些样本")
    
    print("\n🔍 关键代码分析:")
    print("""
    # 1. 找出测试集中的所有异常样本
    anomaly_index = [index for index in range(len(test_dataset)) 
                     if test_dataset[index]["is_anomaly"]]
    
    # 2. 计算噪声样本数量
    train_length = len(train_dataset)
    noise_number = int(noise * train_length)
    
    # 3. 随机选取噪声样本
    noise_index = random.sample(anomaly_index, noise_number)
    
    # 4. 创建噪声数据集并合并到训练集
    noise_dataset = Subset(test_dataset, noise_index)
    train_dataset = ConcatDataset([train_dataset, noise_dataset])
    """)


def demonstrate_noise_selection(dataset_name="visa", data_path="./visa", 
                               subdataset="capsules", noise_ratio=0.1):
    """演示噪声选取过程"""
    print(f"\n📊 演示噪声选取过程 - {dataset_name}/{subdataset}")
    print("=" * 60)
    
    try:
        # 选择数据集类
        if dataset_name.lower() == "mvtec":
            DatasetClass = MVTecDataset
        elif dataset_name.lower() == "visa":
            DatasetClass = VISADataset
        else:
            raise ValueError(f"不支持的数据集: {dataset_name}")
        
        # 创建训练和测试数据集
        train_dataset = DatasetClass(
            source=data_path,
            classname=subdataset,
            split=DatasetSplit.TRAIN
        )
        
        test_dataset = DatasetClass(
            source=data_path,
            classname=subdataset,
            split=DatasetSplit.TEST
        )
        
        print(f"📈 数据集统计:")
        print(f"  训练集大小: {len(train_dataset)}")
        print(f"  测试集大小: {len(test_dataset)}")
        
        # 分析测试集中的异常样本
        anomaly_samples = []
        normal_samples = []
        anomaly_types = defaultdict(list)
        
        for i in range(len(test_dataset)):
            sample = test_dataset[i]
            if sample["is_anomaly"]:
                anomaly_samples.append(i)
                anomaly_types[sample["anomaly"]].append(i)
            else:
                normal_samples.append(i)
        
        print(f"\n🔍 测试集样本分析:")
        print(f"  正常样本数量: {len(normal_samples)}")
        print(f"  异常样本数量: {len(anomaly_samples)}")
        print(f"  异常类型分布:")
        
        for anomaly_type, indices in anomaly_types.items():
            print(f"    {anomaly_type}: {len(indices)} 个样本")
        
        # 模拟噪声选取过程
        noise_number = int(noise_ratio * len(train_dataset))
        print(f"\n⚙️ 噪声选取参数:")
        print(f"  噪声比例: {noise_ratio}")
        print(f"  需要选取的噪声样本数: {noise_number}")
        
        if noise_number > len(anomaly_samples):
            print(f"  ⚠️ 警告: 需要的噪声样本数({noise_number}) > 可用异常样本数({len(anomaly_samples)})")
            noise_number = len(anomaly_samples)
            print(f"  调整为: {noise_number}")
        
        # 随机选取噪声样本
        random.seed(42)  # 固定种子以便重现
        selected_noise_indices = random.sample(anomaly_samples, noise_number)
        
        print(f"\n🎯 选取结果:")
        print(f"  选取的噪声样本索引: {selected_noise_indices[:10]}{'...' if len(selected_noise_indices) > 10 else ''}")
        
        # 分析选取的样本类型分布
        selected_types = defaultdict(int)
        for idx in selected_noise_indices:
            sample = test_dataset[idx]
            selected_types[sample["anomaly"]] += 1
        
        print(f"  选取的异常类型分布:")
        for anomaly_type, count in selected_types.items():
            total_of_type = len(anomaly_types[anomaly_type])
            percentage = (count / total_of_type) * 100
            print(f"    {anomaly_type}: {count}/{total_of_type} ({percentage:.1f}%)")
        
        return selected_noise_indices, anomaly_types
        
    except Exception as e:
        print(f"✗ 演示失败: {e}")
        return None, None


def analyze_existing_noise_files():
    """分析现有的噪声索引文件"""
    print(f"\n📁 分析现有噪声索引文件")
    print("=" * 60)
    
    noise_dir = "noise_index"
    if not os.path.exists(noise_dir):
        print("噪声索引目录不存在")
        return
    
    noise_files = []
    for root, dirs, files in os.walk(noise_dir):
        for file in files:
            if file.endswith('.pth'):
                noise_files.append(os.path.join(root, file))
    
    if not noise_files:
        print("未找到噪声索引文件")
        return
    
    print(f"找到 {len(noise_files)} 个噪声索引文件:")
    
    for file_path in sorted(noise_files):
        try:
            indices = torch.load(file_path, map_location='cpu')
            rel_path = os.path.relpath(file_path, noise_dir)
            
            # 解析文件名信息
            parts = rel_path.split('/')
            if len(parts) >= 2:
                folder_info = parts[0]  # 如 visa_noise0.1_fold0
                file_info = parts[1]    # 如 capsules-noise0.1.pth
                
                # 提取信息
                dataset_info = folder_info.split('_')
                dataset_name = dataset_info[0] if len(dataset_info) > 0 else "unknown"
                noise_ratio = dataset_info[1].replace('noise', '') if len(dataset_info) > 1 else "unknown"
                fold = dataset_info[2].replace('fold', '') if len(dataset_info) > 2 else "0"
                
                subdataset = file_info.split('-')[0] if '-' in file_info else "unknown"
                
                print(f"\n📄 {rel_path}")
                print(f"    数据集: {dataset_name}")
                print(f"    子数据集: {subdataset}")
                print(f"    噪声比例: {noise_ratio}")
                print(f"    折数: {fold}")
                print(f"    选取样本数: {len(indices)}")
                print(f"    样本索引范围: {min(indices)} - {max(indices)}")
                
        except Exception as e:
            print(f"✗ 读取文件失败 {file_path}: {e}")


def compare_selection_strategies():
    """对比不同的选取策略"""
    print(f"\n🔄 对比不同选取策略")
    print("=" * 60)
    
    print("1. 📊 当前策略 - 随机选取:")
    print("   优点: 简单、无偏、可重现")
    print("   缺点: 可能不平衡各异常类型")
    
    print("\n2. 🎯 可能的改进策略:")
    print("   a) 按类型比例选取: 保持各异常类型的原始比例")
    print("   b) 均匀选取: 每种异常类型选取相同数量")
    print("   c) 加权选取: 根据异常类型的稀有程度加权")
    print("   d) 困难样本优先: 选取更难检测的异常样本")
    
    print("\n3. 📈 选取策略对比示例:")
    
    # 模拟数据
    anomaly_types = {
        "crack": 50,
        "scratch": 30, 
        "hole": 20,
        "dent": 10
    }
    
    total_anomalies = sum(anomaly_types.values())
    noise_number = 22  # 假设需要选取22个样本
    
    print(f"   假设异常类型分布: {anomaly_types}")
    print(f"   需要选取: {noise_number} 个样本")
    
    # 随机选取（当前方法）
    print(f"\n   随机选取结果（期望值）:")
    for atype, count in anomaly_types.items():
        expected = (count / total_anomalies) * noise_number
        print(f"     {atype}: {expected:.1f} 个")
    
    # 按比例选取
    print(f"\n   按比例选取结果:")
    for atype, count in anomaly_types.items():
        proportional = int((count / total_anomalies) * noise_number)
        print(f"     {atype}: {proportional} 个")
    
    # 均匀选取
    print(f"\n   均匀选取结果:")
    uniform_per_type = noise_number // len(anomaly_types)
    remainder = noise_number % len(anomaly_types)
    for i, (atype, count) in enumerate(anomaly_types.items()):
        uniform_count = uniform_per_type + (1 if i < remainder else 0)
        available = min(uniform_count, count)
        print(f"     {atype}: {available} 个 (最多{count}个可用)")


def main():
    """主函数"""
    print("SoftPatch噪声选取机制详细分析")
    
    # 1. 分析选取机制
    analyze_noise_selection_mechanism()
    
    # 2. 演示选取过程
    try:
        demonstrate_noise_selection("visa", "./visa", "capsules", 0.1)
    except Exception as e:
        print(f"演示失败: {e}")
        try:
            demonstrate_noise_selection("mvtec", "./mvtec_anomaly_detection", "bottle", 0.1)
        except Exception as e2:
            print(f"MVTec演示也失败: {e2}")
    
    # 3. 分析现有文件
    analyze_existing_noise_files()
    
    # 4. 对比选取策略
    compare_selection_strategies()
    
    print(f"\n" + "=" * 60)
    print("总结")
    print("=" * 60)
    print("🎯 噪声选取特点:")
    print("1. 只从测试集的异常样本中选取")
    print("2. 选取数量 = 噪声比例 × 训练集大小")
    print("3. 使用随机采样，不区分异常类型")
    print("4. 选取结果保存在noise_index目录中")
    print("5. 可以选择是否从测试集中移除选取的样本")
    
    print("\n💡 关键理解:")
    print("- 这不是按类别选取，而是从所有异常样本中随机选取")
    print("- 选取的都是异常样本，不包含正常样本")
    print("- 目的是让模型学会在训练时识别和降权这些噪声")
    print("- 这是SoftPatch论文的核心思想：处理含噪声的训练数据")


if __name__ == "__main__":
    main()
