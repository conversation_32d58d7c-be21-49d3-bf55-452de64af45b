#!/usr/bin/env python3
"""
调试形状错误的脚本
"""

import os
import sys
import torch
import numpy as np
import traceback

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_feature_shapes():
    """调试特征形状问题"""
    print("开始调试特征形状问题...")
    
    try:
        import src.softpatch as softpatch
        import src.backbones as backbones
        import src.common as common
        import src.sampler as sampler
        
        # 设置设备
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {device}")
        
        # 创建模型
        model = softpatch.SoftPatch(device)
        backbone = backbones.load('wideresnet50')
        
        # 创建采样器
        feature_sampler = sampler.ApproximateGreedyCoresetSampler(
            percentage=0.1, device=device
        )
        nn_method = common.FaissNN(False, 4)
        
        # 测试不同的对齐方法
        alignment_methods = [
            (False, 'spatial'),  # 原始方法
            (True, 'spatial'),   # 改进空间对齐
            (True, 'hungarian'), # 匈牙利算法
            (True, 'adaptive'),  # 自适应对齐
        ]
        
        for enable_improved, method in alignment_methods:
            print(f"\n{'='*50}")
            print(f"测试方法: {'改进' if enable_improved else '原始'}_{method}")
            print(f"{'='*50}")
            
            try:
                # 加载模型
                model.load(
                    backbone=backbone,
                    layers_to_extract_from=['layer2', 'layer3'],
                    device=device,
                    input_shape=(3, 224, 224),
                    featuresampler=feature_sampler,
                    nn_method=nn_method,
                    lof_k=6,
                    threshold=0.15,
                    weight_method='lof',
                    soft_weight_flag=True,
                    enable_improved_alignment=enable_improved,
                    alignment_method=method,
                )
                
                # 测试不同的输入尺寸
                test_sizes = [
                    (1, 3, 224, 224),
                    (2, 3, 224, 224),
                    (4, 3, 224, 224),
                    (8, 3, 224, 224),
                ]
                
                for batch_size, channels, height, width in test_sizes:
                    print(f"\n测试输入尺寸: {(batch_size, channels, height, width)}")
                    
                    try:
                        # 创建测试输入
                        test_input = torch.randn(batch_size, channels, height, width).to(device)
                        
                        # 进行前向传播
                        with torch.no_grad():
                            features, patch_shapes = model._embed(test_input, provide_patch_shapes=True)
                        
                        print(f"  ✓ 成功 - 特征形状: {[f.shape if isinstance(f, torch.Tensor) else np.array(f).shape for f in features]}")
                        print(f"  ✓ Patch形状: {patch_shapes}")
                        
                    except Exception as e:
                        print(f"  ✗ 失败: {e}")
                        print(f"  错误详情:")
                        
                        # 尝试获取更详细的错误信息
                        try:
                            # 获取原始特征
                            with torch.no_grad():
                                raw_features = model.forward_modules["feature_aggregator"](test_input)
                            
                            layer_features = [raw_features[layer] for layer in model.layers_to_extract_from]
                            print(f"    原始层特征形状: {[f.shape for f in layer_features]}")
                            
                            # 获取patch信息
                            patch_features = [
                                model.patch_maker.patchify(x, return_spatial_info=True) for x in layer_features
                            ]
                            patch_shapes_debug = [x[1] for x in patch_features]
                            features_debug = [x[0] for x in patch_features]
                            
                            print(f"    Patch后特征形状: {[f.shape for f in features_debug]}")
                            print(f"    Patch形状信息: {patch_shapes_debug}")
                            
                            # 计算预期的batch size
                            for i, (feat, shape) in enumerate(zip(features_debug, patch_shapes_debug)):
                                total_patches = shape[0] * shape[1]
                                expected_batch = feat.shape[0] // total_patches
                                print(f"    层{i}: 特征{feat.shape}, Patch{shape}, 预期batch={expected_batch}")
                                
                        except Exception as inner_e:
                            print(f"    无法获取详细信息: {inner_e}")
                        
                        # 打印完整的错误堆栈
                        print(f"    完整错误:")
                        traceback.print_exc()
                        
            except Exception as e:
                print(f"✗ 方法 {method} 初始化失败: {e}")
                traceback.print_exc()
                
    except ImportError as e:
        print(f"✗ 导入模块失败: {e}")
        return False
    
    return True

def suggest_fixes():
    """建议修复方案"""
    print(f"\n{'='*60}")
    print("建议的修复方案:")
    print(f"{'='*60}")
    
    print("1. 检查输入数据:")
    print("   - 确保batch size > 0")
    print("   - 确保输入图像尺寸正确 (通常是224x224)")
    print("   - 检查数据加载器的batch_size设置")
    
    print("\n2. 检查模型配置:")
    print("   - 确保layers_to_extract_from设置正确")
    print("   - 检查patchsize和stride设置")
    print("   - 验证backbone模型是否正确加载")
    
    print("\n3. 使用安全模式:")
    print("   - 先使用原始对齐方法测试: --enable_improved_alignment=False")
    print("   - 如果原始方法也失败，检查基础配置")
    print("   - 逐步启用改进的对齐方法")
    
    print("\n4. 调试命令:")
    print("   python debug_shape_error.py  # 运行此脚本")
    print("   python quick_test_alignment.py --skip-performance  # 快速测试")
    
    print("\n5. 如果问题持续:")
    print("   - 检查CUDA内存是否足够")
    print("   - 尝试减小batch_size")
    print("   - 检查数据集路径和格式")

def main():
    """主函数"""
    print("特征形状错误调试工具")
    print("=" * 60)
    
    # 运行调试
    success = debug_feature_shapes()
    
    # 提供修复建议
    suggest_fixes()
    
    if success:
        print(f"\n{'='*60}")
        print("调试完成。请查看上面的错误信息和建议。")
        print("=" * 60)
    else:
        print(f"\n{'='*60}")
        print("调试过程中遇到严重错误，请检查环境配置。")
        print("=" * 60)

if __name__ == "__main__":
    main()
