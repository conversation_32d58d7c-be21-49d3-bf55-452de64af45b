Row Names,image_auroc,pixel_auroc

visa_candle,0.9650000000000001,0.9973213868092063

visa_capsules,0.9175438596491228,0.997356351551884

visa_cashew,0.9762500000000001,0.933108910140193

visa_chewinggum,0.9890625000000001,0.9915804784799096

visa_fryum,0.9815625,0.9211655691180629

visa_macaroni1,0.9803571428571428,0.99953016335696

visa_macaroni2,0.8846428571428572,0.9989245560715689

visa_pcb1,0.9817857142857144,0.9973216216315914

visa_pcb2,0.9767857142857143,0.7562220645602951

visa_pcb3,0.9939886845827439,0.9743840679217151

visa_pcb4,0.9883309759547383,0.9669459758899878

visa_pipe_fryum,0.9846874999999999,0.9871534324681147

Mean,0.9683331207298362,0.9600845481666241

 python main.py --dataset visa --data_path ./visa --noise 0.08  "${dataset_flags[@]}" --seed 0 --gpu 0 --resize 512 --imagesize 512 --sampling_ratio 0.01 --subdatasets candle --subdatasets capsules --subdatasets cashew --subdatasets chewinggum --subdatasets fryum --subdatasets macaroni1 --subdatasets macaroni2 --subdatasets pcb1 --subdatasets pcb2 
--subdatasets pcb3 --subdatasets pcb4 --subdatasets pipe_fryum

python main.py --dataset visa --data_path ./visa --enable_improved_alignment --alignment_method spatial     --resize 512 --imagesize 512 --sampling_ratio 0.01 --gpu 0  --noise 0.08  --subdatasets candle --subdatasets capsules --subdatasets cashew --subdatasets chewinggum --subdatasets fryum --subdatasets macaroni1 --subdatasets macaroni2 --subdatasets pcb1 --subdatasets pcb2 