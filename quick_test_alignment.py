#!/usr/bin/env python3
"""
快速测试改进的对齐方法
"""

import os
import sys
import torch
import numpy as np
import argparse
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_alignment_methods():
    """测试对齐方法的基本功能"""
    print("测试改进的对齐方法...")

    # 导入必要的模块
    try:
        import src.softpatch as softpatch
        import src.backbones as backbones
        import src.common as common
        import src.sampler as sampler
        print("✓ 成功导入所有模块")
    except ImportError as e:
        print(f"✗ 导入模块失败: {e}")
        return False

    # 测试设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"✓ 使用设备: {device}")

    try:
        # 测试加载backbone
        backbone = backbones.load('wideresnet50')
        print("✓ 成功加载backbone")

        # 测试不同的对齐方法
        alignment_methods = [
            (False, 'spatial'),  # 原始方法
            (True, 'spatial'),   # 改进空间对齐
            (True, 'hungarian'), # 匈牙利算法
            (True, 'adaptive'),  # 自适应对齐
        ]

        success_count = 0

        for enable_improved, method in alignment_methods:
            method_name = f"{'改进' if enable_improved else '原始'}_{method}"
            try:
                # 创建模型实例
                model = softpatch.SoftPatch(device)

                # 创建采样器和NN方法
                feature_sampler = sampler.ApproximateGreedyCoresetSampler(
                    percentage=0.1, device=device
                )
                nn_method = common.FaissNN(False, 4)

                # 加载模型
                model.load(
                    backbone=backbone,
                    layers_to_extract_from=['layer2', 'layer3'],
                    device=device,
                    input_shape=(3, 224, 224),
                    featuresampler=feature_sampler,
                    nn_method=nn_method,
                    lof_k=6,
                    threshold=0.15,
                    weight_method='lof',
                    soft_weight_flag=True,
                    enable_improved_alignment=enable_improved,
                    alignment_method=method,
                )
                print(f"✓ 成功加载模型 - {method_name}")

                # 测试前向传播 - 使用较小的batch size
                test_input = torch.randn(1, 3, 224, 224).to(device)
                with torch.no_grad():
                    features = model._embed(test_input)
                print(f"✓ 成功进行前向传播 - {method_name}")
                success_count += 1

            except Exception as e:
                print(f"✗ {method_name} 测试失败: {e}")
                # 不立即返回False，继续测试其他方法
                continue

        if success_count > 0:
            print(f"✓ {success_count}/{len(alignment_methods)} 个对齐方法测试通过")
            return True
        else:
            print("✗ 所有对齐方法测试失败")
            return False

    except Exception as e:
        print(f"✗ 模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_alignment_quality():
    """测试对齐质量"""
    print("\n测试对齐质量...")
    
    try:
        # 创建合成数据
        batch_size = 4
        feature1 = torch.randn(batch_size, 14, 14, 256)
        feature2 = torch.randn(batch_size, 7, 7, 512)
        
        # 模拟对齐方法
        import torch.nn.functional as F
        
        # 原始方法
        feature2_reshaped = feature2.permute(0, 3, 1, 2)
        aligned_original = F.interpolate(
            feature2_reshaped, size=(14, 14), mode="bilinear", align_corners=False
        )
        
        # 改进方法
        aligned_improved = F.interpolate(
            feature2_reshaped, size=(14, 14), mode="bilinear", align_corners=True
        )
        
        # 计算对齐质量差异
        diff_original = torch.norm(aligned_original - feature2_reshaped).item()
        diff_improved = torch.norm(aligned_improved - feature2_reshaped).item()
        
        print(f"✓ 原始方法插值差异: {diff_original:.4f}")
        print(f"✓ 改进方法插值差异: {diff_improved:.4f}")
        
        if diff_improved < diff_original:
            print("✓ 改进方法显示更好的对齐质量")
        else:
            print("! 改进方法在此测试中未显示明显优势")
        
        return True
        
    except Exception as e:
        print(f"✗ 对齐质量测试失败: {e}")
        return False

def test_performance_impact():
    """测试性能影响"""
    print("\n测试性能影响...")
    
    try:
        import time
        
        # 创建测试数据
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        test_input = torch.randn(8, 3, 224, 224).to(device)
        
        # 测试不同方法的时间开销
        methods = [
            (False, 'spatial'),  # 原始方法
            (True, 'spatial'),   # 改进空间对齐
            (True, 'hungarian'), # 匈牙利算法
            (True, 'adaptive'),  # 自适应对齐
        ]
        
        times = {}
        
        for enable_improved, method in methods:
            try:
                import src.softpatch as softpatch
                import src.backbones as backbones
                import src.common as common
                import src.sampler as sampler
                
                model = softpatch.SoftPatch(device)
                backbone = backbones.load('wideresnet50')
                feature_sampler = sampler.ApproximateGreedyCoresetSampler(
                    percentage=0.1, device=device
                )
                nn_method = common.FaissNN(False, 4)
                
                model.load(
                    backbone=backbone,
                    layers_to_extract_from=['layer2', 'layer3'],
                    device=device,
                    input_shape=(3, 224, 224),
                    featuresampler=feature_sampler,
                    nn_method=nn_method,
                    lof_k=6,
                    threshold=0.15,
                    weight_method='lof',
                    soft_weight_flag=True,
                    enable_improved_alignment=enable_improved,
                    alignment_method=method,
                )
                
                # 预热
                with torch.no_grad():
                    _ = model._embed(test_input[:2])
                
                # 测量时间
                start_time = time.time()
                with torch.no_grad():
                    for _ in range(5):
                        _ = model._embed(test_input)
                end_time = time.time()
                
                avg_time = (end_time - start_time) / 5
                method_name = f"{'改进' if enable_improved else '原始'}_{method}"
                times[method_name] = avg_time
                
                print(f"✓ {method_name}: {avg_time:.4f}秒")
                
            except Exception as e:
                print(f"✗ {method} 性能测试失败: {e}")
        
        # 计算性能开销
        if '原始_spatial' in times:
            baseline = times['原始_spatial']
            for method, exec_time in times.items():
                if method != '原始_spatial':
                    overhead = (exec_time - baseline) / baseline * 100
                    print(f"  {method} 相对开销: {overhead:+.1f}%")
        
        return True
        
    except Exception as e:
        print(f"✗ 性能测试失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='快速测试改进的对齐方法')
    parser.add_argument('--skip-performance', action='store_true', help='跳过性能测试')
    args = parser.parse_args()
    
    print("=" * 60)
    print("改进的特征对齐方法 - 快速测试")
    print("=" * 60)
    
    success = True
    
    # 测试基本功能
    if not test_alignment_methods():
        success = False
    
    # 测试对齐质量
    if not test_alignment_quality():
        success = False
    
    # 测试性能影响（可选）
    if not args.skip_performance:
        if not test_performance_impact():
            success = False
    else:
        print("\n跳过性能测试")
    
    print("\n" + "=" * 60)
    if success:
        print("✓ 所有测试通过！改进的对齐方法可以正常使用")
        print("\n下一步:")
        print("1. 运行完整测试: python test_improved_alignment.py")
        print("2. 使用改进方法训练: python main.py --enable_improved_alignment")
        print("3. 查看详细说明: IMPROVED_ALIGNMENT_README.md")
    else:
        print("✗ 部分测试失败，请检查错误信息")
    print("=" * 60)

if __name__ == "__main__":
    main()
