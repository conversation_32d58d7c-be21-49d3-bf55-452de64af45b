import logging
import os
import pickle
import tqdm

import torch
# import common
# import sampler
from .common import NetworkFeatureAggregator, Preprocessing,FaissNN,Aggregator,RescaleSegmentor,NearestNeighbourScorer
from .sampler import ApproximateGreedyCoresetSampler,WeightedGreedyCoresetSampler
# import multi_variate_gaussian
from .multi_variate_gaussian import MultiVariateGaussian
from sklearn.neighbors import LocalOutlierFactor
# import backbones
import torch.nn.functional as F
import numpy as np
from .backbones import load
# from torch_cluster import graclus_cluster
import matplotlib.pyplot as plt
from scipy.optimize import linear_sum_assignment
import cv2
import warnings

LOGGER = logging.getLogger(__name__)


class SoftPatch(torch.nn.Module):
    def __init__(self, device):
        super(SoftPatch, self).__init__()
        self.device = device

    def load(
        self,
        backbone,
        device,
        input_shape,
        layers_to_extract_from=("layer2", "layer2"),
        pretrain_embed_dimension=1024,
        target_embed_dimension=1024,
        patchsize=3,
        patchstride=1,
        anomaly_score_num_nn=1,
        featuresampler=ApproximateGreedyCoresetSampler(percentage=0.1, device=torch.device("cuda")),
        nn_method=FaissNN(False, 4),
            lof_k=5,
            threshold=0.15,
            weight_method="lof",
            soft_weight_flag=True,
            enable_improved_alignment=True,
            alignment_method="hungarian",
        **kwargs,
    ):
        self.backbone = backbone.to(device)
        self.layers_to_extract_from = layers_to_extract_from
        self.input_shape = input_shape

        self.device = device
        self.patch_maker = PatchMaker(patchsize, stride=patchstride)

        self.forward_modules = torch.nn.ModuleDict({})

        feature_aggregator =NetworkFeatureAggregator(
            self.backbone, self.layers_to_extract_from, self.device
        )
        feature_dimensions = feature_aggregator.feature_dimensions(input_shape)
        self.forward_modules["feature_aggregator"] = feature_aggregator

        preprocessing =Preprocessing(
            feature_dimensions, pretrain_embed_dimension
        )
        self.forward_modules["preprocessing"] = preprocessing

        self.target_embed_dimension = target_embed_dimension
        preadapt_aggregator = Aggregator(
            target_dim=target_embed_dimension
        )

        _ = preadapt_aggregator.to(self.device)

        self.forward_modules["preadapt_aggregator"] = preadapt_aggregator

        self.anomaly_scorer = NearestNeighbourScorer(
            n_nearest_neighbours=anomaly_score_num_nn, nn_method=nn_method
        )

        self.anomaly_segmentor = RescaleSegmentor(
            device=self.device, target_size=input_shape[-2:]
        )

        self.featuresampler = featuresampler

        ############SoftPatch ##########
        self.featuresampler = WeightedGreedyCoresetSampler(featuresampler.percentage,
                                                                   featuresampler.device)
        self.patch_weight = None
        self.feature_shape = []
        self.lof_k = lof_k
        self.threshold = threshold
        self.coreset_weight = None
        self.weight_method = weight_method
        self.soft_weight_flag = soft_weight_flag

        # 改进的对齐参数
        self.enable_improved_alignment = enable_improved_alignment
        self.alignment_method = alignment_method

    def _compute_feature_similarity(self, features1, features2):
        """计算两个特征之间的相似度矩阵"""
        # 使用余弦相似度
        features1_norm = F.normalize(features1, p=2, dim=-1)
        features2_norm = F.normalize(features2, p=2, dim=-1)
        similarity = torch.mm(features1_norm, features2_norm.t())
        return similarity

    def _check_patch_alignment_quality(self, features, patch_shapes, threshold=0.8):
        """
        检查patch对齐质量

        Args:
            features: 特征列表
            patch_shapes: patch形状列表
            threshold: 对齐质量阈值，超过此值认为已经对齐

        Returns:
            dict: 包含对齐质量信息的字典
                - is_aligned: bool, 是否已经对齐
                - quality_score: float, 对齐质量分数 (0-1)
                - spatial_consistency: float, 空间一致性分数
                - feature_similarity: float, 特征相似性分数
                - details: dict, 详细信息
        """
        if len(features) < 2:
            return {
                'is_aligned': True,
                'quality_score': 1.0,
                'spatial_consistency': 1.0,
                'feature_similarity': 1.0,
                'details': {'reason': 'single_feature_layer'}
            }

        try:
            # 1. 检查空间一致性
            spatial_score = self._evaluate_spatial_consistency(features, patch_shapes)

            # 2. 检查特征相似性
            feature_score = self._evaluate_feature_similarity(features)

            # 3. 计算综合质量分数
            quality_score = 0.6 * spatial_score + 0.4 * feature_score

            # 4. 判断是否已经对齐
            is_aligned = quality_score >= threshold

            return {
                'is_aligned': is_aligned,
                'quality_score': quality_score,
                'spatial_consistency': spatial_score,
                'feature_similarity': feature_score,
                'details': {
                    'threshold': threshold,
                    'num_features': len(features),
                    'patch_shapes': patch_shapes
                }
            }

        except Exception as e:
            warnings.warn(f"对齐质量检查失败: {e}")
            return {
                'is_aligned': False,
                'quality_score': 0.0,
                'spatial_consistency': 0.0,
                'feature_similarity': 0.0,
                'details': {'error': str(e)}
            }

    def _evaluate_spatial_consistency(self, features, patch_shapes):
        """
        评估空间一致性

        Args:
            features: 特征列表
            patch_shapes: patch形状列表

        Returns:
            float: 空间一致性分数 (0-1)
        """
        if len(features) < 2:
            return 1.0

        try:
            # 检查patch形状的一致性
            reference_shape = patch_shapes[0]
            shape_consistency_scores = []

            for i, shape in enumerate(patch_shapes[1:], 1):
                # 计算形状比例的一致性
                ratio_h = min(reference_shape[0], shape[0]) / max(reference_shape[0], shape[0])
                ratio_w = min(reference_shape[1], shape[1]) / max(reference_shape[1], shape[1])
                shape_score = (ratio_h + ratio_w) / 2
                shape_consistency_scores.append(shape_score)

            # 检查特征维度的一致性
            reference_feature = features[0]
            dim_consistency_scores = []

            for i, feature in enumerate(features[1:], 1):
                expected_patches = patch_shapes[i][0] * patch_shapes[i][1]
                if feature.shape[0] % expected_patches == 0:
                    dim_score = 1.0
                else:
                    # 计算维度匹配度
                    actual_ratio = feature.shape[0] / expected_patches
                    dim_score = 1.0 / (1.0 + abs(actual_ratio - 1.0))
                dim_consistency_scores.append(dim_score)

            # 综合空间一致性分数
            shape_score = np.mean(shape_consistency_scores) if shape_consistency_scores else 1.0
            dim_score = np.mean(dim_consistency_scores) if dim_consistency_scores else 1.0

            return 0.7 * shape_score + 0.3 * dim_score

        except Exception as e:
            warnings.warn(f"空间一致性评估失败: {e}")
            return 0.0

    def _evaluate_feature_similarity(self, features):
        """
        评估特征相似性

        Args:
            features: 特征列表

        Returns:
            float: 特征相似性分数 (0-1)
        """
        if len(features) < 2:
            return 1.0

        try:
            reference_feature = features[0]
            similarity_scores = []

            for i, feature in enumerate(features[1:], 1):
                # 确保特征维度匹配
                min_size = min(reference_feature.shape[0], feature.shape[0])
                ref_sample = reference_feature[:min_size]
                feat_sample = feature[:min_size]

                # 计算特征相似性
                if ref_sample.shape[1] == feat_sample.shape[1]:
                    # 直接计算相似性
                    similarity = self._compute_feature_similarity(ref_sample, feat_sample)
                    # 计算对角线相似性的平均值
                    diagonal_sim = torch.diag(similarity).mean().item()
                    similarity_scores.append(max(0.0, diagonal_sim))
                else:
                    # 特征维度不匹配，相似性较低
                    similarity_scores.append(0.3)

            return np.mean(similarity_scores) if similarity_scores else 1.0

        except Exception as e:
            warnings.warn(f"特征相似性评估失败: {e}")
            return 0.0

    def _hungarian_alignment(self, reference_features, target_features):
        """使用匈牙利算法进行特征对齐"""
        # 计算距离矩阵（负相似度）
        similarity = self._compute_feature_similarity(reference_features, target_features)
        cost_matrix = -similarity.cpu().numpy()

        # 使用匈牙利算法找到最优匹配
        row_indices, col_indices = linear_sum_assignment(cost_matrix)

        return col_indices

    def _spatial_alignment(self, features, patch_shapes):
        """基于空间位置的对齐方法"""
        aligned_features = []
        reference_shape = patch_shapes[0]
        reference_feature = features[0]

        # 计算参考特征的目标大小
        ref_total_patches = reference_shape[0] * reference_shape[1]
        ref_batch_size = reference_feature.shape[0] // ref_total_patches
        target_feature_size = ref_total_patches * ref_batch_size

        for i, (feature, shape) in enumerate(zip(features, patch_shapes)):
            if i == 0:
                aligned_features.append(feature)
                continue

            # 检查特征形状和patch形状的兼容性
            total_patches = shape[0] * shape[1]
            if feature.shape[0] % total_patches != 0:
                print(f"Warning: Feature shape {feature.shape} incompatible with patch shape {shape}")
                # 尝试调整特征大小以匹配参考特征
                if feature.shape[0] > target_feature_size:
                    # 截断特征
                    adjusted_feature = feature[:target_feature_size]
                elif feature.shape[0] < target_feature_size:
                    # 填充特征（重复最后一个特征）
                    padding_size = target_feature_size - feature.shape[0]
                    # 创建正确维度的repeat参数
                    repeat_dims = [padding_size] + [1] * (len(feature.shape) - 1)
                    padding = feature[-1:].repeat(*repeat_dims)
                    adjusted_feature = torch.cat([feature, padding], dim=0)
                else:
                    adjusted_feature = feature
                aligned_features.append(adjusted_feature)
                continue

            # 重塑为空间形状
            batch_size = feature.shape[0] // total_patches
            if batch_size <= 0:
                print(f"Warning: Invalid batch size {batch_size}, using fallback alignment")
                # 调整特征大小以匹配参考特征
                if feature.shape[0] != target_feature_size:
                    if feature.shape[0] > target_feature_size:
                        adjusted_feature = feature[:target_feature_size]
                    else:
                        padding_size = target_feature_size - feature.shape[0]
                        # 创建正确维度的repeat参数
                        repeat_dims = [padding_size] + [1] * (len(feature.shape) - 1)
                        padding = feature[-1:].repeat(*repeat_dims)
                        adjusted_feature = torch.cat([feature, padding], dim=0)
                    aligned_features.append(adjusted_feature)
                else:
                    aligned_features.append(feature)
                continue

            try:
                spatial_feature = feature.view(batch_size, shape[0], shape[1], -1)

                # 使用双线性插值进行空间对齐，但使用align_corners=True提高精度
                spatial_feature = spatial_feature.permute(0, 3, 1, 2)  # [B, C, H, W]
                aligned_spatial = F.interpolate(
                    spatial_feature,
                    size=(reference_shape[0], reference_shape[1]),
                    mode="bilinear",
                    align_corners=True  # 改进：使用align_corners=True
                )
                aligned_spatial = aligned_spatial.permute(0, 2, 3, 1)  # [B, H, W, C]
                aligned_feature = aligned_spatial.view(-1, aligned_spatial.shape[-1])

                # 确保对齐后的特征大小与参考特征一致
                if aligned_feature.shape[0] != target_feature_size:
                    if aligned_feature.shape[0] > target_feature_size:
                        aligned_feature = aligned_feature[:target_feature_size]
                    else:
                        padding_size = target_feature_size - aligned_feature.shape[0]
                        # 创建正确维度的repeat参数
                        repeat_dims = [padding_size] + [1] * (len(aligned_feature.shape) - 1)
                        padding = aligned_feature[-1:].repeat(*repeat_dims)
                        aligned_feature = torch.cat([aligned_feature, padding], dim=0)

                aligned_features.append(aligned_feature)

            except Exception as e:
                print(f"Warning: Spatial alignment failed for layer {i}: {e}")
                # 调整特征大小以匹配参考特征
                if feature.shape[0] != target_feature_size:
                    if feature.shape[0] > target_feature_size:
                        adjusted_feature = feature[:target_feature_size]
                    else:
                        padding_size = target_feature_size - feature.shape[0]
                        # 创建正确维度的repeat参数
                        repeat_dims = [padding_size] + [1] * (len(feature.shape) - 1)
                        padding = feature[-1:].repeat(*repeat_dims)
                        adjusted_feature = torch.cat([feature, padding], dim=0)
                    aligned_features.append(adjusted_feature)
                else:
                    aligned_features.append(feature)

        return aligned_features

    def _adaptive_alignment(self, features, patch_shapes):
        """自适应对齐方法，结合空间和特征对齐"""
        # 首先进行空间对齐
        spatially_aligned = self._spatial_alignment(features, patch_shapes)

        # 验证空间对齐后的特征大小一致性
        if len(spatially_aligned) > 1:
            reference_features = spatially_aligned[0]
            reference_size = reference_features.shape[0]

            # 确保所有特征都有相同的大小
            for i in range(1, len(spatially_aligned)):
                if spatially_aligned[i].shape[0] != reference_size:
                    print(f"Warning: Feature size mismatch after spatial alignment at layer {i}: "
                          f"{spatially_aligned[i].shape[0]} vs {reference_size}")
                    # 调整大小以匹配参考特征
                    if spatially_aligned[i].shape[0] > reference_size:
                        spatially_aligned[i] = spatially_aligned[i][:reference_size]
                    else:
                        padding_size = reference_size - spatially_aligned[i].shape[0]
                        # 创建正确维度的repeat参数
                        repeat_dims = [padding_size] + [1] * (len(spatially_aligned[i].shape) - 1)
                        padding = spatially_aligned[i][-1:].repeat(*repeat_dims)
                        spatially_aligned[i] = torch.cat([spatially_aligned[i], padding], dim=0)

            # 然后进行特征级别的细调对齐
            for i in range(1, len(spatially_aligned)):
                target_features = spatially_aligned[i]

                # 再次检查特征维度是否匹配
                if reference_features.shape[0] != target_features.shape[0]:
                    print(f"Warning: Feature dimension mismatch at layer {i}, skipping Hungarian alignment")
                    continue

                # 检查特征维度是否过大（避免内存问题）
                if reference_features.shape[0] > 10000:
                    print(f"Warning: Feature size too large ({reference_features.shape[0]}), skipping Hungarian alignment")
                    continue

                try:
                    # 使用匈牙利算法进行全局对齐
                    alignment_indices = self._hungarian_alignment(reference_features, target_features)
                    spatially_aligned[i] = target_features[alignment_indices]
                except Exception as e:
                    # 如果对齐失败，保持空间对齐的结果
                    print(f"Warning: Adaptive alignment failed for layer {i}, using spatial alignment only: {e}")
                    continue

        return spatially_aligned

    def embed(self, data):
        if isinstance(data, torch.utils.data.DataLoader):
            features = []
            for image in data:
                if isinstance(image, dict):
                    image = image["image"]
                with torch.no_grad():
                    input_image = image.to(torch.float).to(self.device)
                    features.append(self._embed(input_image))
            return features
        return self._embed(data)

    def _embed(self, images, detach=True, provide_patch_shapes=False):
        """Returns feature embeddings for images."""

        def _detach(features):
            if detach:
                return [x.detach().cpu().numpy() for x in features]
            return features

        _ = self.forward_modules["feature_aggregator"].eval()
        with torch.no_grad():
            features = self.forward_modules["feature_aggregator"](images)

        features = [features[layer] for layer in self.layers_to_extract_from]

        features = [
            self.patch_maker.patchify(x, return_spatial_info=True) for x in features
        ]
        patch_shapes = [x[1] for x in features]
        features = [x[0] for x in features]
        ref_num_patches = patch_shapes[0]

        # 改进的特征对齐
        if self.enable_improved_alignment:
            # 首先检查是否需要对齐
            alignment_check = self._check_patch_alignment_quality(features, patch_shapes)

            if alignment_check['is_aligned']:
                print(f"✓ Patch已经对齐 (质量分数: {alignment_check['quality_score']:.3f}), 跳过对齐步骤")
            else:
                print(f"⚠ Patch需要对齐 (质量分数: {alignment_check['quality_score']:.3f})")
                print(f"  - 空间一致性: {alignment_check['spatial_consistency']:.3f}")
                print(f"  - 特征相似性: {alignment_check['feature_similarity']:.3f}")

                if self.alignment_method == "spatial":
                    # 使用改进的空间对齐方法
                    features = self._spatial_alignment(features, patch_shapes)
                elif self.alignment_method == "adaptive":
                    # 使用自适应对齐方法
                    features = self._adaptive_alignment(features, patch_shapes)
                else:
                    # 默认使用空间对齐
                    features = self._spatial_alignment(features, patch_shapes)

                # 对齐后再次检查质量
                post_alignment_check = self._check_patch_alignment_quality(features, patch_shapes)
                print(f"✓ 对齐完成 (质量分数: {post_alignment_check['quality_score']:.3f})")
        else:
            # 即使使用原始方法，也可以检查对齐质量
            alignment_check = self._check_patch_alignment_quality(features, patch_shapes)
            if not alignment_check['is_aligned']:
                print(f"⚠ 检测到对齐问题 (质量分数: {alignment_check['quality_score']:.3f})")
                print("  建议启用改进的对齐方法: --enable_improved_alignment")
        else:
            # 原始的插值对齐方法
            for i in range(1, len(features)):
                _features = features[i]
                patch_dims = patch_shapes[i]

                try:
                    _features = _features.reshape(
                        _features.shape[0], patch_dims[0], patch_dims[1], *_features.shape[2:]
                    )
                    _features = _features.permute(0, -3, -2, -1, 1, 2)
                    perm_base_shape = _features.shape
                    _features = _features.reshape(-1, *_features.shape[-2:])
                    _features = F.interpolate(
                        _features.unsqueeze(1),
                        size=(ref_num_patches[0], ref_num_patches[1]),
                        mode="bilinear",
                        align_corners=False,
                    )
                    _features = _features.squeeze(1)
                    _features = _features.reshape(
                        *perm_base_shape[:-2], ref_num_patches[0], ref_num_patches[1]
                    )
                    _features = _features.permute(0, -2, -1, 1, 2, 3)
                    _features = _features.reshape(len(_features), -1, *_features.shape[-3:])
                    features[i] = _features
                except Exception as e:
                    print(f"Warning: Original alignment failed for layer {i}: {e}")
                    print(f"Feature shape: {features[i].shape}, Patch dims: {patch_dims}")
                    # 保持原始特征不变
                    continue

        features = [x.reshape(-1, *x.shape[-3:]) for x in features]

        # As different feature backbones & patching provide differently
        # sized features, these are brought into the correct form here.
        features = self.forward_modules["preprocessing"](features)
        features = self.forward_modules["preadapt_aggregator"](features)

        if provide_patch_shapes:
            return _detach(features), patch_shapes
        return _detach(features)

    def fit(self, training_data):
        """
        This function computes the embeddings of the training data and fills the
        memory bank of SPADE.
        """
        self._fill_memory_bank(training_data)

    def _fill_memory_bank(self, input_data):
        """Computes and sets the support features for SPADE."""
        _ = self.forward_modules.eval()

        def _image_to_features(input_image):
            with torch.no_grad():
                input_image = input_image.to(torch.float).to(self.device)
                return self._embed(input_image)

        features = []
        with tqdm.tqdm(
            input_data, desc="Computing support features...", leave=True
        ) as data_iterator:
            for image in data_iterator:
                if isinstance(image, dict):
                    image = image["image"]
                features.append(_image_to_features(image))

        features = np.concatenate(features, axis=0)

        with torch.no_grad():
            # pdb.set_trace()
            self.feature_shape = self._embed(image.to(torch.float).to(self.device), provide_patch_shapes=True)[1][0]
            patch_weight = self._compute_patch_weight(features)

            # normalization
            # patch_weight = (patch_weight - patch_weight.quantile(0.5, dim=1, keepdim=True)).reshape(-1) + 1

            patch_weight = patch_weight.reshape(-1)
            threshold = torch.quantile(patch_weight, 1 - self.threshold)
            sampling_weight = torch.where(patch_weight > threshold, 0, 1)
            self.featuresampler.set_sampling_weight(sampling_weight)
            self.patch_weight = patch_weight.clamp(min=0)

            sample_features, sample_indices = self.featuresampler.run(features)
            features = sample_features
            self.coreset_weight = self.patch_weight[sample_indices].cpu().numpy()

        self.anomaly_scorer.fit(detection_features=[features])

    def _compute_patch_weight(self, features: np.ndarray):
        if isinstance(features, np.ndarray):
            features = torch.from_numpy(features)

        reduced_features = self.featuresampler._reduce_features(features)

        # 检查特征形状和feature_shape的兼容性
        expected_patch_size = self.feature_shape[0] * self.feature_shape[1]
        if reduced_features.shape[0] % expected_patch_size != 0:
            print(f"Warning: Feature size {reduced_features.shape[0]} not divisible by patch size {expected_patch_size}")
            # 调整特征大小
            target_size = (reduced_features.shape[0] // expected_patch_size) * expected_patch_size
            if target_size > 0:
                reduced_features = reduced_features[:target_size]
            else:
                print(f"Error: Cannot reshape features with size {reduced_features.shape[0]} to patch size {expected_patch_size}")
                # 返回默认权重
                return torch.ones(1, reduced_features.shape[0]).to(self.device)

        try:
            patch_features = reduced_features.reshape(-1, expected_patch_size, reduced_features.shape[-1])
        except Exception as e:
            print(f"Error reshaping features: {e}")
            print(f"Feature shape: {reduced_features.shape}, Expected patch size: {expected_patch_size}")
            # 返回默认权重
            return torch.ones(1, reduced_features.shape[0]).to(self.device)

        # 改进的特征对齐
        assign = []
        if self.enable_improved_alignment and self.alignment_method == "hungarian" and patch_features.shape[0] > 1:
            codebook = patch_features[0]
            for i in range(1, patch_features.shape[0]):
                try:
                    col_ind = self._hungarian_alignment(codebook, patch_features[i])
                    assign.append(col_ind)
                    patch_features[i] = torch.index_select(patch_features[i], 0, torch.from_numpy(col_ind).to(self.device))
                except Exception as e:
                    print(f"Warning: Hungarian alignment failed for patch {i}: {e}")
                    continue

        patch_features = patch_features.permute(1, 0, 2)

        if self.weight_method == "lof":
            patch_weight = self._compute_lof(self.lof_k, patch_features).transpose(-1, -2)
        elif self.weight_method == "lof_gpu":
            patch_weight = self._compute_lof_gpu(self.lof_k, patch_features).transpose(-1, -2)
        elif self.weight_method == "nearest":
            patch_weight = self._compute_nearest_distance(patch_features).transpose(-1, -2)
            patch_weight = patch_weight + 1
        elif self.weight_method == "gaussian":
            gaussian = MultiVariateGaussian(patch_features.shape[2], patch_features.shape[0])
            stats = gaussian.fit(patch_features)
            patch_weight = self._compute_distance_with_gaussian(patch_features, stats).transpose(-1, -2)
            patch_weight = patch_weight + 1
        else:
            raise ValueError("Unexpected weight method")

        # 改进的权重对齐
        if self.enable_improved_alignment and self.alignment_method == "hungarian" and len(assign) > 0:
            patch_weight = patch_weight.cpu().numpy()
            for i in range(0, len(assign)):
                # 重新排列权重以匹配对齐的特征
                original_weight = patch_weight[i+1].copy()
                patch_weight[i+1][assign[i]] = original_weight
            patch_weight = torch.from_numpy(patch_weight).to(self.device)

        return patch_weight

   # def _compute_distance_with_gaussian(self, embedding: torch.Tensor, stats: [torch.Tensor]) -> torch.Tensor:
    def _compute_distance_with_gaussian(self, embedding: torch.Tensor, stats: list[torch.Tensor]) -> torch.Tensor:
        """
        Args:
            embedding (Tensor): Embedding Vector
            stats (List[Tensor]): Mean and Covariance Matrix of the multivariate Gaussian distribution

        Returns:
            Anomaly score of a test image via mahalanobis distance.
        """
        # patch, batch, channel = embedding.shape
        embedding = embedding.permute(1, 2, 0)

        mean, inv_covariance = stats
        delta = (embedding - mean).permute(2, 0, 1)

        distances = (torch.matmul(delta, inv_covariance) * delta).sum(2)
        distances = torch.sqrt(distances)

        return distances

    def _compute_nearest_distance(self, embedding: torch.Tensor) -> torch.Tensor:
        patch, batch, _ = embedding.shape

        x_x = (embedding ** 2).sum(dim=-1, keepdim=True).expand(patch, batch, batch)
        dist_mat = (x_x + x_x.transpose(-1, -2) - 2 * embedding.matmul(embedding.transpose(-1, -2))).abs() ** 0.5
        nearest_distance = torch.topk(dist_mat, dim=-1, largest=False, k=2)[0].sum(dim=-1)  #
        # nearest_distance = nearest_distance.transpose(0, 1).reshape(batch * patch)
        return nearest_distance

    def _compute_lof(self, k, embedding: torch.Tensor) -> torch.Tensor:
        patch, batch, _ = embedding.shape   # 784x219x128
        clf = LocalOutlierFactor(n_neighbors=int(k), metric='l2')
        scores = torch.zeros(size=(patch, batch), device=embedding.device)
        for i in range(patch):
            clf.fit(embedding[i].cpu())
            scores[i] = torch.Tensor(- clf.negative_outlier_factor_)
            # scores[i] = scores[i] / scores[i].mean()   # normalization
        # embedding = embedding.reshape(patch*batch, channel)
        # clf.fit(embedding.cpu())
        # scores = torch.Tensor(- clf.negative_outlier_factor_)
        # scores = scores.reshape(patch, batch)
        return scores

    def _compute_lof_gpu(self, k, embedding: torch.Tensor) -> torch.Tensor:
        """
        GPU support
        """

        patch, batch, _ = embedding.shape

        # calculate distance
        x_x = (embedding ** 2).sum(dim=-1, keepdim=True).expand(patch, batch, batch)
        dist_mat = (x_x + x_x.transpose(-1, -2) - 2 * embedding.matmul(embedding.transpose(-1, -2))).abs() ** 0.5 + 1e-6

        # find neighborhoods
        top_k_distance_mat, top_k_index = torch.topk(dist_mat, dim=-1, largest=False, k=k + 1)
        top_k_distance_mat, top_k_index = top_k_distance_mat[:, :, 1:], top_k_index[:, :, 1:]
        k_distance_value_mat = top_k_distance_mat[:, :, -1]

        # calculate reachability distance
        reach_dist_mat = torch.max(dist_mat, k_distance_value_mat.unsqueeze(2).expand(patch, batch, batch)
                                   .transpose(-1, -2))  # Transposing is important
        top_k_index_hot = torch.zeros(size=dist_mat.shape, device=top_k_index.device).scatter_(-1, top_k_index, 1)

        # Local reachability density
        lrd_mat = k / (top_k_index_hot * reach_dist_mat).sum(dim=-1)

        # calculate local outlier factor
        lof_mat = ((lrd_mat.unsqueeze(2).expand(patch, batch, batch).transpose(-1, -2) * top_k_index_hot).sum(
            dim=-1) / k) / lrd_mat
        return lof_mat


    def _chunk_lof(self, k, embedding: torch.Tensor) -> torch.Tensor:
        width, height, batch, channel = embedding.shape
        chunk_size = 2

        new_width, new_height = int(width / chunk_size), int(height / chunk_size)
        new_patch = new_width * new_height
        new_batch = batch * chunk_size * chunk_size

        split_width = torch.stack(embedding.split(chunk_size, dim=0), dim=0)
        split_height = torch.stack(split_width.split(chunk_size, dim=1 + 1), dim=1)

        new_embedding = split_height.view(new_patch, new_batch, channel)
        lof_mat = self._compute_lof(k, new_embedding)
        chunk_lof_mat = lof_mat.reshape(new_width, new_height, chunk_size, chunk_size, batch)
        chunk_lof_mat = chunk_lof_mat.transpose(1, 2).reshape(width, height, batch)
        return chunk_lof_mat

    def predict(self, data):
        if isinstance(data, torch.utils.data.DataLoader):
            return self._predict_dataloader(data)
        return self._predict(data)

    def _predict_dataloader(self, dataloader):
        """This function provides anomaly scores/maps for full dataloaders."""
        _ = self.forward_modules.eval()

        scores = []
        masks = []
        labels_gt = []
        masks_gt = []
        
        
        with tqdm.tqdm(dataloader, desc="Inferring...", leave=True) as data_iterator:
            for image in data_iterator:
                if isinstance(image, dict):
                    labels_gt.extend(image["is_anomaly"].numpy().tolist())
                    masks_gt.extend(image["mask"].numpy().tolist())
                    image = image["image"]
                _scores, _masks = self._predict(image)
                for score, mask in zip(_scores, _masks):
                    scores.append(score)
                    masks.append(mask)
        return scores, masks, labels_gt, masks_gt

    def _predict(self, images):
        """Infer score and mask for a batch of images."""
        images = images.to(torch.float).to(self.device)
        _ = self.forward_modules.eval()

        batchsize = images.shape[0]
        with torch.no_grad():
            features, patch_shapes = self._embed(images, provide_patch_shapes=True)
            features = np.asarray(features)

            image_scores, _, indices = self.anomaly_scorer.predict([features])
            if self.soft_weight_flag:
                indices = indices.squeeze()
                weight = np.take(self.coreset_weight, axis=0, indices=indices)
                image_scores = image_scores * weight

            patch_scores = image_scores

            image_scores = self.patch_maker.unpatch_scores(
                image_scores, batchsize=batchsize
            )
            image_scores = image_scores.reshape(*image_scores.shape[:2], -1)
            image_scores = self.patch_maker.score(image_scores)

            patch_scores = self.patch_maker.unpatch_scores(
                patch_scores, batchsize=batchsize
            )
            scales = patch_shapes[0]
            patch_scores = patch_scores.reshape(batchsize, scales[0], scales[1])

            masks = self.anomaly_segmentor.convert_to_segmentation(patch_scores)

           

        return [score for score in image_scores], [mask for mask in masks]



    @staticmethod
    def _params_file(filepath, prepend=""):
        return os.path.join(filepath, prepend + "params.pkl")

    def save_to_path(self, save_path: str, prepend: str = "") -> None:
        LOGGER.info("Saving data.")
        self.anomaly_scorer.save(
            save_path, save_features_separately=False, prepend=prepend
        )
        params = {
            "backbone.name": self.backbone.name,
            "layers_to_extract_from": self.layers_to_extract_from,
            "input_shape": self.input_shape,
            "pretrain_embed_dimension": self.forward_modules[
                "preprocessing"
            ].output_dim,
            "target_embed_dimension": self.forward_modules[
                "preadapt_aggregator"
            ].target_dim,
            "patchsize": self.patch_maker.patchsize,
            "patchstride": self.patch_maker.stride,
            "anomaly_scorer_num_nn": self.anomaly_scorer.n_nearest_neighbours,
        }
        with open(self._params_file(save_path, prepend), "wb") as save_file:
            pickle.dump(params, save_file, pickle.HIGHEST_PROTOCOL)
'''    def load_from_path(
        self,
        load_path: str,
        device: torch.device,
        nn_method: common.FaissNN(False, 4),
        prepend: str = "",
    ) -> None:
        LOGGER.info("Loading and initializing.")
        with open(self._params_file(load_path, prepend), "rb") as load_file:
            params = pickle.load(load_file)
        params["backbone"] = backbones.load(
            params["backbone.name"]
        )
        params["backbone"].name = params["backbone.name"]
        del params["backbone.name"]
        self.load(**params, device=device, nn_method=nn_method)

        self.anomaly_scorer.load(load_path, prepend)
'''
from typing import Optional

def load_from_path(
    self,
    load_path: str,
    device: torch.device,
    nn_method: Optional[FaissNN] = None,  # ✅ 正确：使用 Optional 类型
    prepend: str = "",
) -> None:
    LOGGER.info("Loading and initializing.")
    with open(self._params_file(load_path, prepend), "rb") as load_file:
        params = pickle.load(load_file)
    params["backbone"] = load(
        params["backbone.name"]
    )
    params["backbone"].name = params["backbone.name"]
    del params["backbone.name"]

    if nn_method is None:
        nn_method = FaissNN(False, 4)  # ✅ 在函数内部初始化

    self.load(**params, device=device, nn_method=nn_method)

# Image handling classes.
class PatchMaker:
    def __init__(self, patchsize, stride=None):
        self.patchsize = patchsize
        self.stride = stride

    def patchify(self, features, return_spatial_info=False):
        """Convert a tensor into a tensor of respective patches.
        Args:
            x: [torch.Tensor, bs x c x w x h]
        Returns:
            x: [torch.Tensor, bs * w//stride * h//stride, c, patchsize,
            patchsize]
        """
        padding = int((self.patchsize - 1) / 2)
        unfolder = torch.nn.Unfold(
            kernel_size=self.patchsize, stride=self.stride, padding=padding, dilation=1
        )
        unfolded_features = unfolder(features)
        number_of_total_patches = []
        for side in features.shape[-2:]:
            n_patches = (
                side + 2 * padding - 1 * (self.patchsize - 1) - 1
            ) / self.stride + 1
            number_of_total_patches.append(int(n_patches))
        unfolded_features = unfolded_features.reshape(
            *features.shape[:2], self.patchsize, self.patchsize, -1
        )
        unfolded_features = unfolded_features.permute(0, 4, 1, 2, 3)

        if return_spatial_info:
            return unfolded_features, number_of_total_patches
        return unfolded_features

    def unpatch_scores(self, patch_scores, batchsize):
        return patch_scores.reshape(batchsize, -1, *patch_scores.shape[1:])

    def score(self, image_scores):
        was_numpy = False
        if isinstance(image_scores, np.ndarray):
            was_numpy = True
            image_scores = torch.from_numpy(image_scores)
        while image_scores.ndim > 1:
            image_scores = torch.max(image_scores, dim=-1).values
        if was_numpy:
            return image_scores.numpy()
        return image_scores


