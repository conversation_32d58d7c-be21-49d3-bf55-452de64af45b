#!/usr/bin/env python3
"""
Patch对齐质量评估工具

这个模块提供了评估patch对齐质量的工具函数，包括：
1. 空间一致性检查
2. 特征相似性检查
3. 对齐质量综合评估
4. 可视化对齐状态
"""

import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
import warnings
from typing import List, Dict, Tuple, Optional


class AlignmentChecker:
    """Patch对齐质量检查器"""
    
    def __init__(self, device='cpu'):
        self.device = device
        
    def check_alignment_quality(self, features: List[torch.Tensor], 
                              patch_shapes: List[Tuple[int, int]], 
                              threshold: float = 0.8) -> Dict:
        """
        检查patch对齐质量
        
        Args:
            features: 特征列表
            patch_shapes: patch形状列表
            threshold: 对齐质量阈值，超过此值认为已经对齐
            
        Returns:
            dict: 包含对齐质量信息的字典
        """
        if len(features) < 2:
            return {
                'is_aligned': True,
                'quality_score': 1.0,
                'spatial_consistency': 1.0,
                'feature_similarity': 1.0,
                'details': {'reason': 'single_feature_layer'}
            }
        
        try:
            # 1. 检查空间一致性
            spatial_score = self.evaluate_spatial_consistency(features, patch_shapes)
            
            # 2. 检查特征相似性
            feature_score = self.evaluate_feature_similarity(features)
            
            # 3. 计算综合质量分数
            quality_score = 0.6 * spatial_score + 0.4 * feature_score
            
            # 4. 判断是否已经对齐
            is_aligned = quality_score >= threshold
            
            return {
                'is_aligned': is_aligned,
                'quality_score': quality_score,
                'spatial_consistency': spatial_score,
                'feature_similarity': feature_score,
                'details': {
                    'threshold': threshold,
                    'num_features': len(features),
                    'patch_shapes': patch_shapes
                }
            }
            
        except Exception as e:
            warnings.warn(f"对齐质量检查失败: {e}")
            return {
                'is_aligned': False,
                'quality_score': 0.0,
                'spatial_consistency': 0.0,
                'feature_similarity': 0.0,
                'details': {'error': str(e)}
            }

    def evaluate_spatial_consistency(self, features: List[torch.Tensor], 
                                   patch_shapes: List[Tuple[int, int]]) -> float:
        """
        评估空间一致性
        
        Args:
            features: 特征列表
            patch_shapes: patch形状列表
            
        Returns:
            float: 空间一致性分数 (0-1)
        """
        if len(features) < 2:
            return 1.0
            
        try:
            # 检查patch形状的一致性
            reference_shape = patch_shapes[0]
            shape_consistency_scores = []
            
            for i, shape in enumerate(patch_shapes[1:], 1):
                # 计算形状比例的一致性
                ratio_h = min(reference_shape[0], shape[0]) / max(reference_shape[0], shape[0])
                ratio_w = min(reference_shape[1], shape[1]) / max(reference_shape[1], shape[1])
                shape_score = (ratio_h + ratio_w) / 2
                shape_consistency_scores.append(shape_score)
            
            # 检查特征维度的一致性
            dim_consistency_scores = []
            
            for i, feature in enumerate(features[1:], 1):
                expected_patches = patch_shapes[i][0] * patch_shapes[i][1]
                if feature.shape[0] % expected_patches == 0:
                    dim_score = 1.0
                else:
                    # 计算维度匹配度
                    actual_ratio = feature.shape[0] / expected_patches
                    dim_score = 1.0 / (1.0 + abs(actual_ratio - 1.0))
                dim_consistency_scores.append(dim_score)
            
            # 综合空间一致性分数
            shape_score = np.mean(shape_consistency_scores) if shape_consistency_scores else 1.0
            dim_score = np.mean(dim_consistency_scores) if dim_consistency_scores else 1.0
            
            return 0.7 * shape_score + 0.3 * dim_score
            
        except Exception as e:
            warnings.warn(f"空间一致性评估失败: {e}")
            return 0.0

    def evaluate_feature_similarity(self, features: List[torch.Tensor]) -> float:
        """
        评估特征相似性
        
        Args:
            features: 特征列表
            
        Returns:
            float: 特征相似性分数 (0-1)
        """
        if len(features) < 2:
            return 1.0
            
        try:
            reference_feature = features[0]
            similarity_scores = []
            
            for i, feature in enumerate(features[1:], 1):
                # 确保特征维度匹配
                min_size = min(reference_feature.shape[0], feature.shape[0])
                ref_sample = reference_feature[:min_size]
                feat_sample = feature[:min_size]
                
                # 计算特征相似性
                if ref_sample.shape[1] == feat_sample.shape[1]:
                    # 直接计算相似性
                    similarity = self._compute_feature_similarity(ref_sample, feat_sample)
                    # 计算对角线相似性的平均值
                    diagonal_sim = torch.diag(similarity).mean().item()
                    similarity_scores.append(max(0.0, diagonal_sim))
                else:
                    # 特征维度不匹配，相似性较低
                    similarity_scores.append(0.3)
            
            return np.mean(similarity_scores) if similarity_scores else 1.0
            
        except Exception as e:
            warnings.warn(f"特征相似性评估失败: {e}")
            return 0.0

    def _compute_feature_similarity(self, features1: torch.Tensor, 
                                  features2: torch.Tensor) -> torch.Tensor:
        """计算两个特征之间的相似度矩阵"""
        # 使用余弦相似度
        features1_norm = F.normalize(features1, p=2, dim=-1)
        features2_norm = F.normalize(features2, p=2, dim=-1)
        similarity = torch.mm(features1_norm, features2_norm.t())
        return similarity

    def visualize_alignment_status(self, features: List[torch.Tensor], 
                                 patch_shapes: List[Tuple[int, int]], 
                                 save_path: Optional[str] = None) -> None:
        """
        可视化对齐状态
        
        Args:
            features: 特征列表
            patch_shapes: patch形状列表
            save_path: 保存路径，如果为None则显示图像
        """
        try:
            # 检查对齐质量
            alignment_info = self.check_alignment_quality(features, patch_shapes)
            
            # 创建可视化
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))
            fig.suptitle(f'Patch对齐状态分析\n质量分数: {alignment_info["quality_score"]:.3f}', 
                        fontsize=14, fontweight='bold')
            
            # 1. 特征形状分析
            axes[0, 0].bar(range(len(features)), [f.shape[0] for f in features])
            axes[0, 0].set_title('特征数量对比')
            axes[0, 0].set_xlabel('层级')
            axes[0, 0].set_ylabel('特征数量')
            
            # 2. Patch形状分析
            patch_areas = [shape[0] * shape[1] for shape in patch_shapes]
            axes[0, 1].bar(range(len(patch_shapes)), patch_areas)
            axes[0, 1].set_title('Patch区域大小')
            axes[0, 1].set_xlabel('层级')
            axes[0, 1].set_ylabel('Patch数量')
            
            # 3. 质量分数分解
            scores = ['空间一致性', '特征相似性', '综合质量']
            values = [
                alignment_info['spatial_consistency'],
                alignment_info['feature_similarity'],
                alignment_info['quality_score']
            ]
            colors = ['blue', 'green', 'red']
            bars = axes[1, 0].bar(scores, values, color=colors, alpha=0.7)
            axes[1, 0].set_title('质量分数分解')
            axes[1, 0].set_ylabel('分数')
            axes[1, 0].set_ylim(0, 1)
            
            # 添加数值标签
            for bar, value in zip(bars, values):
                axes[1, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                               f'{value:.3f}', ha='center', va='bottom')
            
            # 4. 对齐状态总结
            axes[1, 1].axis('off')
            status_text = f"""
对齐状态: {'✓ 已对齐' if alignment_info['is_aligned'] else '✗ 需要对齐'}

详细信息:
• 特征层数: {len(features)}
• 空间一致性: {alignment_info['spatial_consistency']:.3f}
• 特征相似性: {alignment_info['feature_similarity']:.3f}
• 综合质量: {alignment_info['quality_score']:.3f}

建议:
{'无需额外处理' if alignment_info['is_aligned'] else '建议使用改进的对齐方法'}
            """
            axes[1, 1].text(0.1, 0.9, status_text, transform=axes[1, 1].transAxes,
                           fontsize=10, verticalalignment='top',
                           bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"对齐状态图已保存到: {save_path}")
            else:
                plt.show()
                
        except Exception as e:
            warnings.warn(f"可视化失败: {e}")

    def generate_alignment_report(self, features: List[torch.Tensor], 
                                patch_shapes: List[Tuple[int, int]]) -> str:
        """
        生成对齐质量报告
        
        Args:
            features: 特征列表
            patch_shapes: patch形状列表
            
        Returns:
            str: 对齐质量报告
        """
        alignment_info = self.check_alignment_quality(features, patch_shapes)
        
        report = f"""
=== Patch对齐质量报告 ===

总体状态: {'✓ 已对齐' if alignment_info['is_aligned'] else '✗ 需要对齐'}
质量分数: {alignment_info['quality_score']:.3f}

详细分析:
• 空间一致性: {alignment_info['spatial_consistency']:.3f}
• 特征相似性: {alignment_info['feature_similarity']:.3f}
• 特征层数: {len(features)}

特征信息:
"""
        
        for i, (feature, shape) in enumerate(zip(features, patch_shapes)):
            report += f"  层级 {i}: 特征形状 {feature.shape}, Patch形状 {shape}\n"
        
        if not alignment_info['is_aligned']:
            report += "\n建议:\n"
            if alignment_info['spatial_consistency'] < 0.7:
                report += "• 空间一致性较低，建议使用空间对齐方法\n"
            if alignment_info['feature_similarity'] < 0.7:
                report += "• 特征相似性较低，建议使用匈牙利算法对齐\n"
            report += "• 启用改进的对齐方法: --enable_improved_alignment\n"
        
        return report
