#!/usr/bin/env python3
"""
数据增强演示脚本

展示新添加的数据增强功能，包括：
1. 图像旋转
2. 仿射变换
3. 颜色抖动
4. 高斯模糊
5. 水平翻转
"""

import os
import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import argparse

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.datasets.mvtec import MVTecDataset, DatasetSplit
from src.datasets.visa import VISADataset


def denormalize_image(tensor, mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]):
    """反归一化图像用于显示"""
    mean = torch.tensor(mean).view(3, 1, 1)
    std = torch.tensor(std).view(3, 1, 1)
    tensor = tensor * std + mean
    tensor = torch.clamp(tensor, 0, 1)
    return tensor


def demo_augmentation_effects(dataset_name="mvtec", data_path="./mvtec_anomaly_detection", 
                            subdataset="bottle", num_samples=3):
    """演示数据增强效果"""
    print(f"演示 {dataset_name} 数据集的数据增强效果")
    print(f"数据路径: {data_path}")
    print(f"子数据集: {subdataset}")
    print("=" * 60)
    
    # 选择数据集类
    if dataset_name.lower() == "mvtec":
        DatasetClass = MVTecDataset
    elif dataset_name.lower() == "visa":
        DatasetClass = VISADataset
    else:
        raise ValueError(f"不支持的数据集: {dataset_name}")
    
    # 创建不同强度的数据增强数据集
    augmentation_configs = [
        {"enable_augmentation": False, "augmentation_strength": 0.0, "name": "原始图像"},
        {"enable_augmentation": True, "augmentation_strength": 0.3, "name": "轻度增强"},
        {"enable_augmentation": True, "augmentation_strength": 0.5, "name": "中度增强"},
        {"enable_augmentation": True, "augmentation_strength": 0.8, "name": "强度增强"},
    ]
    
    datasets = []
    for config in augmentation_configs:
        try:
            dataset = DatasetClass(
                source=data_path,
                classname=subdataset,
                resize=256,
                imagesize=224,
                split=DatasetSplit.TRAIN,
                enable_augmentation=config["enable_augmentation"],
                augmentation_strength=config["augmentation_strength"]
            )
            datasets.append((dataset, config["name"]))
            print(f"✓ 创建数据集: {config['name']}")
        except Exception as e:
            print(f"✗ 创建数据集失败 {config['name']}: {e}")
            continue
    
    if not datasets:
        print("✗ 没有成功创建任何数据集")
        return
    
    # 可视化增强效果
    fig, axes = plt.subplots(num_samples, len(datasets), figsize=(4*len(datasets), 4*num_samples))
    if num_samples == 1:
        axes = axes.reshape(1, -1)
    
    for sample_idx in range(num_samples):
        for dataset_idx, (dataset, name) in enumerate(datasets):
            try:
                # 获取同一个样本的不同增强版本
                if dataset_idx == 0:  # 原始数据集，记录索引
                    base_sample = dataset[sample_idx]
                    sample_path = base_sample.get('image_path', f'Sample {sample_idx}')
                
                sample = dataset[sample_idx]
                image_tensor = sample['image']
                
                # 反归一化用于显示
                image_display = denormalize_image(image_tensor)
                image_np = image_display.permute(1, 2, 0).numpy()
                
                # 显示图像
                ax = axes[sample_idx, dataset_idx]
                ax.imshow(image_np)
                ax.set_title(f"{name}\n{os.path.basename(sample_path) if dataset_idx == 0 else ''}", 
                           fontsize=10)
                ax.axis('off')
                
            except Exception as e:
                print(f"✗ 处理样本 {sample_idx}, 数据集 {name} 失败: {e}")
                continue
    
    plt.tight_layout()
    plt.savefig("data_augmentation_demo.png", dpi=300, bbox_inches='tight')
    print(f"\n✓ 数据增强演示图已保存到: data_augmentation_demo.png")
    plt.show()


def demo_augmentation_parameters():
    """演示不同增强参数的效果"""
    print("\n" + "=" * 60)
    print("数据增强参数说明")
    print("=" * 60)
    
    parameters = [
        {
            "strength": 0.0,
            "description": "无增强",
            "effects": ["原始图像", "无任何变换"]
        },
        {
            "strength": 0.3,
            "description": "轻度增强",
            "effects": [
                "旋转角度: ±4.5°",
                "平移: ±3%",
                "缩放: 0.97-1.03",
                "剪切: ±3°",
                "颜色抖动: 轻微"
            ]
        },
        {
            "strength": 0.5,
            "description": "中度增强",
            "effects": [
                "旋转角度: ±7.5°",
                "平移: ±5%",
                "缩放: 0.95-1.05",
                "剪切: ±5°",
                "水平翻转: 15%概率",
                "颜色抖动: 中等",
                "高斯模糊: 10%概率"
            ]
        },
        {
            "strength": 0.8,
            "description": "强度增强",
            "effects": [
                "旋转角度: ±12°",
                "平移: ±8%",
                "缩放: 0.92-1.08",
                "剪切: ±8°",
                "水平翻转: 24%概率",
                "颜色抖动: 强烈",
                "高斯模糊: 16%概率"
            ]
        }
    ]
    
    for param in parameters:
        print(f"\n强度 {param['strength']}: {param['description']}")
        print("-" * 30)
        for effect in param['effects']:
            print(f"  • {effect}")


def test_augmentation_performance():
    """测试数据增强对性能的影响"""
    print("\n" + "=" * 60)
    print("数据增强性能测试")
    print("=" * 60)
    
    try:
        import time
        
        # 创建测试数据集
        dataset_original = MVTecDataset(
            source="./mvtec_anomaly_detection",
            classname="bottle",
            resize=256,
            imagesize=224,
            split=DatasetSplit.TRAIN,
            enable_augmentation=False
        )
        
        dataset_augmented = MVTecDataset(
            source="./mvtec_anomaly_detection",
            classname="bottle",
            resize=256,
            imagesize=224,
            split=DatasetSplit.TRAIN,
            enable_augmentation=True,
            augmentation_strength=0.5
        )
        
        # 测试加载时间
        num_samples = 50
        
        # 原始数据集
        start_time = time.time()
        for i in range(min(num_samples, len(dataset_original))):
            _ = dataset_original[i]
        original_time = time.time() - start_time
        
        # 增强数据集
        start_time = time.time()
        for i in range(min(num_samples, len(dataset_augmented))):
            _ = dataset_augmented[i]
        augmented_time = time.time() - start_time
        
        print(f"原始数据集加载时间: {original_time:.3f}秒 ({num_samples}个样本)")
        print(f"增强数据集加载时间: {augmented_time:.3f}秒 ({num_samples}个样本)")
        print(f"性能开销: {((augmented_time - original_time) / original_time * 100):+.1f}%")
        
        if augmented_time / original_time < 2.0:
            print("✓ 性能开销可接受")
        else:
            print("⚠ 性能开销较大，建议降低增强强度")
            
    except Exception as e:
        print(f"✗ 性能测试失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='数据增强演示')
    parser.add_argument('--dataset', choices=['mvtec', 'visa'], default='mvtec',
                       help='数据集类型')
    parser.add_argument('--data_path', type=str, 
                       default='./mvtec_anomaly_detection',
                       help='数据集路径')
    parser.add_argument('--subdataset', type=str, default='bottle',
                       help='子数据集名称')
    parser.add_argument('--num_samples', type=int, default=3,
                       help='演示样本数量')
    parser.add_argument('--skip_demo', action='store_true',
                       help='跳过可视化演示')
    parser.add_argument('--skip_performance', action='store_true',
                       help='跳过性能测试')
    
    args = parser.parse_args()
    
    print("数据增强功能演示")
    print("=" * 60)
    
    # 演示增强效果
    if not args.skip_demo:
        try:
            demo_augmentation_effects(
                dataset_name=args.dataset,
                data_path=args.data_path,
                subdataset=args.subdataset,
                num_samples=args.num_samples
            )
        except Exception as e:
            print(f"✗ 可视化演示失败: {e}")
    
    # 演示参数说明
    demo_augmentation_parameters()
    
    # 性能测试
    if not args.skip_performance:
        test_augmentation_performance()
    
    print("\n" + "=" * 60)
    print("使用建议:")
    print("1. 轻度增强 (0.3): 适合大多数异常检测任务")
    print("2. 中度增强 (0.5): 平衡性能和鲁棒性")
    print("3. 强度增强 (0.8): 数据稀少或需要强鲁棒性时使用")
    print("\n运行命令示例:")
    print("python main.py --dataset visa --data_path ./visa --subdatasets capsules \\")
    print("    --enable_augmentation --augmentation_strength 0.5 \\")
    print("    --enable_improved_alignment --alignment_method spatial")
    print("=" * 60)


if __name__ == "__main__":
    main()
