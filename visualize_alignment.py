#!/usr/bin/env python3
"""
Patch对齐状态可视化工具

这个脚本提供了可视化patch对齐状态的功能，帮助用户：
1. 直观地查看patch对齐质量
2. 对比不同对齐方法的效果
3. 调试对齐问题
4. 验证对齐改进效果
"""

import os
import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
import argparse
from PIL import Image
import warnings

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.alignment_checker import AlignmentChecker
import src.softpatch as softpatch
import src.backbones as backbones
import src.common as common
import src.sampler as sampler


def create_test_features():
    """创建测试用的特征数据"""
    # 模拟不同层级的特征
    batch_size = 4
    
    # 第一层特征 (较大分辨率)
    feature1 = torch.randn(batch_size * 14 * 14, 256)  # 14x14 patches
    
    # 第二层特征 (较小分辨率，有偏移)
    feature2 = torch.randn(batch_size * 7 * 7, 512)    # 7x7 patches
    
    # 第三层特征 (更小分辨率)
    feature3 = torch.randn(batch_size * 3 * 3, 1024)   # 3x3 patches
    
    features = [feature1, feature2, feature3]
    patch_shapes = [(14, 14), (7, 7), (3, 3)]
    
    return features, patch_shapes


def visualize_alignment_comparison():
    """对比不同对齐方法的效果"""
    print("创建测试特征数据...")
    features, patch_shapes = create_test_features()
    
    # 创建对齐检查器
    checker = AlignmentChecker()
    
    print("\n=== 原始特征对齐状态 ===")
    original_report = checker.generate_alignment_report(features, patch_shapes)
    print(original_report)
    
    # 可视化原始状态
    print("\n生成原始状态可视化...")
    checker.visualize_alignment_status(features, patch_shapes, 
                                     save_path="alignment_status_original.png")
    
    # 模拟对齐后的特征
    print("\n模拟对齐改进效果...")
    
    # 简单的对齐模拟：调整特征使其更一致
    aligned_features = []
    for i, feature in enumerate(features):
        if i == 0:
            aligned_features.append(feature)
        else:
            # 添加一些噪声来模拟对齐改进
            noise_factor = 0.1 * (1 - i * 0.3)  # 逐层减少噪声
            aligned_feature = feature + torch.randn_like(feature) * noise_factor
            aligned_features.append(aligned_feature)
    
    print("\n=== 对齐后特征状态 ===")
    aligned_report = checker.generate_alignment_report(aligned_features, patch_shapes)
    print(aligned_report)
    
    # 可视化对齐后状态
    print("\n生成对齐后状态可视化...")
    checker.visualize_alignment_status(aligned_features, patch_shapes, 
                                     save_path="alignment_status_improved.png")


def visualize_real_model_alignment(model_path=None, image_path=None):
    """可视化真实模型的对齐状态"""
    try:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {device}")
        
        # 创建模型
        model = softpatch.SoftPatch(device)
        backbone = backbones.load('wideresnet50')
        feature_sampler = sampler.ApproximateGreedyCoresetSampler(
            percentage=0.1, device=device
        )
        nn_method = common.FaissNN(False, 4)
        
        # 加载模型
        model.load(
            backbone=backbone,
            layers_to_extract_from=['layer2', 'layer3'],
            device=device,
            input_shape=(3, 224, 224),
            featuresampler=feature_sampler,
            nn_method=nn_method,
            lof_k=6,
            threshold=0.15,
            weight_method='lof',
            soft_weight_flag=True,
            enable_improved_alignment=True,
            alignment_method='spatial',
        )
        
        print("✓ 模型加载成功")
        
        # 创建测试输入
        if image_path and os.path.exists(image_path):
            # 使用真实图像
            from torchvision import transforms
            transform = transforms.Compose([
                transforms.Resize(256),
                transforms.CenterCrop(224),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                                   std=[0.229, 0.224, 0.225])
            ])
            image = Image.open(image_path).convert('RGB')
            test_input = transform(image).unsqueeze(0).to(device)
            print(f"✓ 使用图像: {image_path}")
        else:
            # 使用随机输入
            test_input = torch.randn(1, 3, 224, 224).to(device)
            print("✓ 使用随机输入")
        
        # 提取特征
        with torch.no_grad():
            features, patch_shapes = model._embed(test_input, detach=False, provide_patch_shapes=True)
        
        print("✓ 特征提取完成")
        
        # 创建对齐检查器并分析
        checker = AlignmentChecker(device=device)
        
        print("\n=== 真实模型对齐状态 ===")
        report = checker.generate_alignment_report(features, patch_shapes)
        print(report)
        
        # 可视化
        print("\n生成真实模型对齐状态可视化...")
        checker.visualize_alignment_status(features, patch_shapes, 
                                         save_path="alignment_status_real_model.png")
        
        return True
        
    except Exception as e:
        print(f"✗ 真实模型可视化失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def create_alignment_dashboard():
    """创建对齐状态仪表板"""
    print("创建对齐状态仪表板...")
    
    # 创建综合可视化
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Patch对齐状态仪表板', fontsize=16, fontweight='bold')
    
    # 模拟不同场景的数据
    scenarios = [
        ("理想对齐", [0.95, 0.92, 0.94]),
        ("轻微偏移", [0.85, 0.78, 0.82]),
        ("严重偏移", [0.45, 0.38, 0.42]),
        ("部分对齐", [0.75, 0.65, 0.70]),
        ("完全错位", [0.25, 0.15, 0.20]),
        ("改进后", [0.88, 0.85, 0.87])
    ]
    
    for i, (scenario, scores) in enumerate(scenarios):
        row = i // 3
        col = i % 3
        
        # 创建雷达图
        categories = ['空间一致性', '特征相似性', '综合质量']
        angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
        scores_plot = scores + [scores[0]]  # 闭合图形
        angles += angles[:1]
        
        ax = axes[row, col]
        ax.plot(angles, scores_plot, 'o-', linewidth=2, label=scenario)
        ax.fill(angles, scores_plot, alpha=0.25)
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(categories, fontsize=8)
        ax.set_ylim(0, 1)
        ax.set_title(scenario, fontweight='bold')
        ax.grid(True)
        
        # 添加分数标签
        for angle, score, category in zip(angles[:-1], scores, categories):
            ax.text(angle, score + 0.05, f'{score:.2f}', 
                   ha='center', va='center', fontsize=8)
    
    plt.tight_layout()
    plt.savefig("alignment_dashboard.png", dpi=300, bbox_inches='tight')
    print("✓ 对齐状态仪表板已保存到: alignment_dashboard.png")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Patch对齐状态可视化工具')
    parser.add_argument('--mode', choices=['comparison', 'real', 'dashboard', 'all'], 
                       default='all', help='可视化模式')
    parser.add_argument('--image', type=str, help='用于真实模型测试的图像路径')
    parser.add_argument('--model', type=str, help='模型路径（可选）')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("Patch对齐状态可视化工具")
    print("=" * 60)
    
    success = True
    
    if args.mode in ['comparison', 'all']:
        print("\n1. 对齐方法对比可视化")
        print("-" * 30)
        try:
            visualize_alignment_comparison()
            print("✓ 对齐方法对比完成")
        except Exception as e:
            print(f"✗ 对齐方法对比失败: {e}")
            success = False
    
    if args.mode in ['real', 'all']:
        print("\n2. 真实模型对齐状态")
        print("-" * 30)
        if not visualize_real_model_alignment(args.model, args.image):
            success = False
    
    if args.mode in ['dashboard', 'all']:
        print("\n3. 对齐状态仪表板")
        print("-" * 30)
        try:
            create_alignment_dashboard()
            print("✓ 仪表板创建完成")
        except Exception as e:
            print(f"✗ 仪表板创建失败: {e}")
            success = False
    
    print("\n" + "=" * 60)
    if success:
        print("✓ 可视化完成！生成的文件:")
        print("  - alignment_status_original.png: 原始对齐状态")
        print("  - alignment_status_improved.png: 改进后对齐状态")
        print("  - alignment_status_real_model.png: 真实模型对齐状态")
        print("  - alignment_dashboard.png: 对齐状态仪表板")
        print("\n使用建议:")
        print("1. 查看生成的图像了解对齐状态")
        print("2. 根据质量分数调整对齐参数")
        print("3. 使用仪表板对比不同场景")
    else:
        print("✗ 部分可视化失败，请检查错误信息")
    print("=" * 60)


if __name__ == "__main__":
    main()
