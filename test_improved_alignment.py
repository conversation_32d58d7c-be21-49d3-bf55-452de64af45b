#!/usr/bin/env python3
"""
测试改进的对齐方法对AUC性能的影响
"""

import os
import sys
import argparse
import logging
import torch
import numpy as np
from sklearn.metrics import roc_auc_score
import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import src.softpatch as softpatch
import src.backbones as backbones
import src.common as common
import src.sampler as sampler
import src.datasets as datasets
import src.metrics as metrics

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def create_test_config():
    """创建测试配置"""
    config = {
        'backbone_name': 'wideresnet50',
        'layers_to_extract_from': ['layer2', 'layer3'],
        'device': torch.device('cuda' if torch.cuda.is_available() else 'cpu'),
        'input_shape': (3, 224, 224),
        'sampling_ratio': 0.1,
        'lof_k': 6,
        'threshold': 0.15,
        'weight_method': 'lof',
        'batch_size': 8,
        'imagesize': 224,
        'resize': 256
    }
    return config

def load_dataset(data_path, dataset_name, subdataset, config):
    """加载数据集"""
    dataset_info = {
        "mvtec": ["src.datasets.mvtec", "MVTecDataset"],
        "visa": ["src.datasets.visa", "VISADataset"]
    }
    
    if dataset_name not in dataset_info:
        raise ValueError(f"Unsupported dataset: {dataset_name}")
    
    dataset_library_file, dataset_class_name = dataset_info[dataset_name]
    dataset_library = __import__(dataset_library_file, fromlist=[dataset_class_name])
    dataset_class = getattr(dataset_library, dataset_class_name)
    
    # 训练数据集
    train_dataset = dataset_class(
        source=data_path,
        classname=subdataset,
        resize=config['resize'],
        imagesize=config['imagesize'],
        split=datasets.mvtec.DatasetSplit.TRAIN,
    )
    
    # 测试数据集
    test_dataset = dataset_class(
        source=data_path,
        classname=subdataset,
        resize=config['resize'],
        imagesize=config['imagesize'],
        split=datasets.mvtec.DatasetSplit.TEST,
    )
    
    train_dataloader = torch.utils.data.DataLoader(
        train_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=0,
        pin_memory=True,
    )
    
    test_dataloader = torch.utils.data.DataLoader(
        test_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=0,
        pin_memory=True,
    )
    
    return train_dataloader, test_dataloader

def create_model(config, enable_improved_alignment=False, alignment_method="spatial"):
    """创建模型"""
    # 加载backbone
    backbone = backbones.load(config['backbone_name'])
    
    # 创建采样器
    device = config['device']
    feature_sampler = sampler.ApproximateGreedyCoresetSampler(
        percentage=config['sampling_ratio'], 
        device=device
    )
    
    # 创建NN方法
    nn_method = common.FaissNN(False, 4, device=device.index if device.type == 'cuda' else None)
    
    # 创建SoftPatch实例
    model = softpatch.SoftPatch(device)
    model.load(
        backbone=backbone,
        layers_to_extract_from=config['layers_to_extract_from'],
        device=device,
        input_shape=config['input_shape'],
        featuresampler=feature_sampler,
        nn_method=nn_method,
        lof_k=config['lof_k'],
        threshold=config['threshold'],
        weight_method=config['weight_method'],
        soft_weight_flag=True,
        enable_improved_alignment=enable_improved_alignment,
        alignment_method=alignment_method,
    )
    
    return model

def evaluate_model(model, train_dataloader, test_dataloader, logger):
    """评估模型性能"""
    logger.info("开始训练模型...")
    start_time = time.time()
    
    # 训练
    model.fit(train_dataloader)
    
    train_time = time.time() - start_time
    logger.info(f"训练完成，耗时: {train_time:.2f}秒")
    
    logger.info("开始测试...")
    start_time = time.time()
    
    # 测试
    scores = []
    labels = []
    segmentations = []
    
    for batch in test_dataloader:
        if isinstance(batch, dict):
            images = batch["image"]
            batch_labels = batch["is_anomaly"]
        else:
            images, batch_labels = batch
            
        with torch.no_grad():
            batch_scores, batch_segmentations = model.predict(images)
            
        scores.extend(batch_scores)
        labels.extend(batch_labels.cpu().numpy())
        segmentations.extend(batch_segmentations)
    
    test_time = time.time() - start_time
    logger.info(f"测试完成，耗时: {test_time:.2f}秒")
    
    # 计算AUC
    try:
        auc = roc_auc_score(labels, scores)
    except ValueError as e:
        logger.warning(f"无法计算AUC: {e}")
        auc = 0.5
    
    return auc, train_time, test_time

def run_comparison_test(data_path, dataset_name, subdataset, logger):
    """运行对比测试"""
    config = create_test_config()
    logger.info(f"测试配置: {config}")
    
    # 加载数据集
    logger.info(f"加载数据集: {dataset_name}/{subdataset}")
    train_dataloader, test_dataloader = load_dataset(data_path, dataset_name, subdataset, config)
    
    results = {}
    
    # 测试原始方法
    logger.info("=" * 50)
    logger.info("测试原始对齐方法")
    logger.info("=" * 50)
    
    model_original = create_model(config, enable_improved_alignment=False)
    auc_original, train_time_orig, test_time_orig = evaluate_model(
        model_original, train_dataloader, test_dataloader, logger
    )
    results['original'] = {
        'auc': auc_original,
        'train_time': train_time_orig,
        'test_time': test_time_orig
    }
    logger.info(f"原始方法 AUC: {auc_original:.4f}")
    
    # 测试改进的空间对齐方法
    logger.info("=" * 50)
    logger.info("测试改进的空间对齐方法")
    logger.info("=" * 50)
    
    model_spatial = create_model(config, enable_improved_alignment=True, alignment_method="spatial")
    auc_spatial, train_time_spatial, test_time_spatial = evaluate_model(
        model_spatial, train_dataloader, test_dataloader, logger
    )
    results['spatial'] = {
        'auc': auc_spatial,
        'train_time': train_time_spatial,
        'test_time': test_time_spatial
    }
    logger.info(f"空间对齐方法 AUC: {auc_spatial:.4f}")
    
    # 测试匈牙利算法对齐方法
    logger.info("=" * 50)
    logger.info("测试匈牙利算法对齐方法")
    logger.info("=" * 50)
    
    model_hungarian = create_model(config, enable_improved_alignment=True, alignment_method="hungarian")
    auc_hungarian, train_time_hung, test_time_hung = evaluate_model(
        model_hungarian, train_dataloader, test_dataloader, logger
    )
    results['hungarian'] = {
        'auc': auc_hungarian,
        'train_time': train_time_hung,
        'test_time': test_time_hung
    }
    logger.info(f"匈牙利算法对齐方法 AUC: {auc_hungarian:.4f}")
    
    # 测试自适应对齐方法
    logger.info("=" * 50)
    logger.info("测试自适应对齐方法")
    logger.info("=" * 50)
    
    model_adaptive = create_model(config, enable_improved_alignment=True, alignment_method="adaptive")
    auc_adaptive, train_time_adapt, test_time_adapt = evaluate_model(
        model_adaptive, train_dataloader, test_dataloader, logger
    )
    results['adaptive'] = {
        'auc': auc_adaptive,
        'train_time': train_time_adapt,
        'test_time': test_time_adapt
    }
    logger.info(f"自适应对齐方法 AUC: {auc_adaptive:.4f}")
    
    return results

def main():
    parser = argparse.ArgumentParser(description='测试改进的对齐方法')
    parser.add_argument('--data_path', type=str, required=True, help='数据集路径')
    parser.add_argument('--dataset', type=str, default='mvtec', choices=['mvtec', 'visa'], help='数据集名称')
    parser.add_argument('--subdataset', type=str, required=True, help='子数据集名称')
    
    args = parser.parse_args()
    
    logger = setup_logging()
    
    logger.info("开始对齐方法对比测试")
    logger.info(f"数据集: {args.dataset}")
    logger.info(f"子数据集: {args.subdataset}")
    
    try:
        results = run_comparison_test(args.data_path, args.dataset, args.subdataset, logger)
        
        # 输出结果总结
        logger.info("=" * 60)
        logger.info("测试结果总结")
        logger.info("=" * 60)
        
        for method, result in results.items():
            logger.info(f"{method:12s}: AUC={result['auc']:.4f}, "
                       f"训练时间={result['train_time']:.2f}s, "
                       f"测试时间={result['test_time']:.2f}s")
        
        # 计算改进幅度
        original_auc = results['original']['auc']
        logger.info("\n改进幅度:")
        for method, result in results.items():
            if method != 'original':
                improvement = (result['auc'] - original_auc) / original_auc * 100
                logger.info(f"{method:12s}: {improvement:+.2f}%")
                
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        raise

if __name__ == "__main__":
    main()
